import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, withAuth } from "@/contexts/auth-context";
import NotFound from "@/pages/not-found";
import Login from "@/pages/login";
import Dashboard from "@/pages/dashboard";
import MetalProcurement from "@/pages/procurement";
import Inventory from "@/pages/inventory";
import Billing from "@/pages/billing";
import Customers from "@/pages/customers";
import Payments from "@/pages/payments";
import Rates from "@/pages/rates";
import Reports from "@/pages/reports";
import Settings from "@/pages/settings";
import UserManagement from "@/pages/users";

// Protected components with role-based access
const ProtectedDashboard = withAuth(Dashboard);
const ProtectedProcurement = withAuth(MetalProcurement, ["admin", "manager"]);
const ProtectedInventory = withAuth(Inventory);
const ProtectedBilling = withAuth(Billing);
const ProtectedCustomers = withAuth(Customers);
const ProtectedPayments = withAuth(Payments);
const ProtectedRates = withAuth(Rates, ["admin", "manager"]);
const ProtectedReports = withAuth(Reports);
const ProtectedSettings = withAuth(Settings, ["admin", "manager"]);
const ProtectedUsers = withAuth(UserManagement, ["admin"]);

function Router() {
  return (
    <Switch>
      <Route path="/login" component={Login} />
      <Route path="/" component={ProtectedDashboard} />
      <Route path="/dashboard" component={ProtectedDashboard} />
      <Route path="/procurement" component={ProtectedProcurement} />
      <Route path="/inventory" component={ProtectedInventory} />
      <Route path="/billing" component={ProtectedBilling} />
      <Route path="/customers" component={ProtectedCustomers} />
      <Route path="/payments" component={ProtectedPayments} />
      <Route path="/rates" component={ProtectedRates} />
      <Route path="/reports" component={ProtectedReports} />
      <Route path="/settings" component={ProtectedSettings} />
      <Route path="/users" component={ProtectedUsers} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
