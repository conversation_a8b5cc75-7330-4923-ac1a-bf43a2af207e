import React, { useState, useEffect } from 'react';
import { Plus, Search, Download, Upload, Copy, Trash2, Star, Settings, Eye, Edit3 } from 'lucide-react';
import { templateManager } from '../../lib/template-manager';
import { InvoiceTemplate, TemplatePreview } from '../../types/template';
import { TemplateEditor } from './TemplateEditor';
import { TemplatePreviewModal } from './TemplatePreviewModal';

export const TemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([]);
  const [previews, setPreviews] = useState<TemplatePreview[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [stats, setStats] = useState({ total: 0, active: 0, custom: 0, system: 0 });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = () => {
    const allTemplates = templateManager.getAllTemplates();
    const templatePreviews = templateManager.getTemplatePreviews();
    const templateStats = templateManager.getTemplateStats();
    
    setTemplates(allTemplates);
    setPreviews(templatePreviews);
    setStats(templateStats);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const filtered = templateManager.searchTemplates(query);
      setTemplates(filtered);
    } else {
      setTemplates(templateManager.getAllTemplates());
    }
  };

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setIsCreating(true);
    setShowEditor(true);
  };

  const handleEditTemplate = (template: InvoiceTemplate) => {
    setSelectedTemplate(template);
    setIsCreating(false);
    setShowEditor(true);
  };

  const handleCloneTemplate = (template: InvoiceTemplate) => {
    const cloned = templateManager.cloneTemplate(template.id, `${template.name} (Copy)`);
    if (cloned) {
      loadTemplates();
    }
  };

  const handleDeleteTemplate = (template: InvoiceTemplate) => {
    if (template.createdBy === 'system') {
      alert('Cannot delete system templates');
      return;
    }
    
    if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
      templateManager.deleteTemplate(template.id);
      loadTemplates();
    }
  };

  const handleSetDefault = (template: InvoiceTemplate) => {
    templateManager.setDefaultTemplate(template.id);
    loadTemplates();
  };

  const handleExportTemplate = (template: InvoiceTemplate) => {
    const exported = templateManager.exportTemplate(template.id);
    if (exported) {
      const blob = new Blob([exported], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name.replace(/\s+/g, '_')}_template.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleImportTemplate = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const imported = templateManager.importTemplate(content);
        if (imported) {
          loadTemplates();
          alert('Template imported successfully!');
        } else {
          alert('Failed to import template. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  const handlePreviewTemplate = (template: InvoiceTemplate) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const filteredTemplates = searchQuery 
    ? templates.filter(t => 
        t.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : templates;

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Template Manager</h1>
            <p className="text-gray-600 mt-1">Create and manage invoice templates</p>
          </div>
          <button
            onClick={handleCreateTemplate}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Template
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Templates</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <div className="text-sm text-gray-600">Active Templates</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-purple-600">{stats.custom}</div>
            <div className="text-sm text-gray-600">Custom Templates</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-orange-600">{stats.system}</div>
            <div className="text-sm text-gray-600">System Templates</div>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="flex gap-4 items-center">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <label className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer transition-colors">
            <Upload className="w-4 h-4" />
            Import
            <input
              type="file"
              accept=".json"
              onChange={handleImportTemplate}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredTemplates.map((template) => (
          <div key={template.id} className="bg-white rounded-lg border hover:shadow-lg transition-shadow">
            {/* Template Preview */}
            <div className="p-4 border-b">
              <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                <img
                  src={previews.find(p => p.id === template.id)?.thumbnail}
                  alt={template.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                    {template.name}
                    {template.isDefault && (
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    )}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      template.createdBy === 'system' 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {template.createdBy === 'system' ? 'System' : 'Custom'}
                    </span>
                    {template.isActive && (
                      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="p-4">
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handlePreviewTemplate(template)}
                  className="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                >
                  <Eye className="w-3 h-3" />
                  Preview
                </button>
                <button
                  onClick={() => handleEditTemplate(template)}
                  className="flex-1 bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                >
                  <Edit3 className="w-3 h-3" />
                  Edit
                </button>
              </div>
              
              <div className="flex gap-2 mt-2">
                {!template.isDefault && (
                  <button
                    onClick={() => handleSetDefault(template)}
                    className="flex-1 bg-yellow-50 hover:bg-yellow-100 text-yellow-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                  >
                    <Star className="w-3 h-3" />
                    Set Default
                  </button>
                )}
                
                <button
                  onClick={() => handleCloneTemplate(template)}
                  className="flex-1 bg-purple-50 hover:bg-purple-100 text-purple-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                >
                  <Copy className="w-3 h-3" />
                  Clone
                </button>
                
                <button
                  onClick={() => handleExportTemplate(template)}
                  className="flex-1 bg-green-50 hover:bg-green-100 text-green-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                >
                  <Download className="w-3 h-3" />
                  Export
                </button>
                
                {template.createdBy === 'user' && (
                  <button
                    onClick={() => handleDeleteTemplate(template)}
                    className="flex-1 bg-red-50 hover:bg-red-100 text-red-700 px-3 py-2 rounded text-sm flex items-center justify-center gap-1 transition-colors"
                  >
                    <Trash2 className="w-3 h-3" />
                    Delete
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? 'No templates found' : 'No templates available'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery 
              ? 'Try adjusting your search criteria'
              : 'Create your first template to get started'
            }
          </p>
          {!searchQuery && (
            <button
              onClick={handleCreateTemplate}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Create Template
            </button>
          )}
        </div>
      )}

      {/* Template Editor Modal */}
      {showEditor && (
        <TemplateEditor
          template={selectedTemplate}
          isCreating={isCreating}
          onSave={(template) => {
            loadTemplates();
            setShowEditor(false);
          }}
          onCancel={() => setShowEditor(false)}
        />
      )}

      {/* Template Preview Modal */}
      {showPreview && selectedTemplate && (
        <TemplatePreviewModal
          template={selectedTemplate}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
};