import React, { useState, useEffect } from 'react';
import { X, Save, Palette, Layout, <PERSON>tings, Eye, FileText, RotateCcw } from 'lucide-react';
import { InvoiceTemplate, CreateTemplateRequest, UpdateTemplateRequest, TemplateColors, TemplateLayout, TemplateSettings, PAPER_SIZES, PaperSize, PaperOrientation, getPaperDimensions } from '../../types/template';
import { templateManager } from '../../lib/template-manager';

interface TemplateEditorProps {
  template: InvoiceTemplate | null;
  isCreating: boolean;
  onSave: (template: InvoiceTemplate) => void;
  onCancel: () => void;
}

export const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  isCreating,
  onSave,
  onCancel
}) => {
  const [activeTab, setActiveTab] = useState<'basic' | 'colors' | 'layout' | 'settings'>('basic');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    colors: {
      primary: [41, 128, 185] as [number, number, number],
      accent: [39, 174, 96] as [number, number, number],
      dark: [44, 62, 80] as [number, number, number],
      lightGray: [236, 240, 241] as [number, number, number],
      mediumGray: [149, 165, 166] as [number, number, number]
    },
    layout: {
      margin: 6,
      headerHeight: 28,
      sectionSpacing: 8,
      fontSize: {
        header: 18,
        subheader: 12,
        body: 8,
        small: 6
      }
    },
    settings: {
      showLogo: true,
      showWatermark: false,
      showGSTBreakdown: false,
      showBankDetails: true,
      showTermsAndConditions: true,
      compactMode: true,
      currency: '₹',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'en-IN'
    }
  });

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description,
        colors: template.colors,
        layout: template.layout,
        settings: template.settings
      });
    }
  }, [template]);

  const handleSave = () => {
    if (!formData.name.trim()) {
      alert('Please enter a template name');
      return;
    }

    try {
      let savedTemplate: InvoiceTemplate | null = null;

      if (isCreating) {
        const createRequest: CreateTemplateRequest = {
          name: formData.name,
          description: formData.description,
          category: 'custom',
          colors: formData.colors,
          layout: formData.layout,
          settings: formData.settings
        };
        savedTemplate = templateManager.createTemplate(createRequest);
      } else if (template) {
        const updateRequest: UpdateTemplateRequest = {
          id: template.id,
          name: formData.name,
          description: formData.description,
          colors: formData.colors,
          layout: formData.layout,
          settings: formData.settings
        };
        savedTemplate = templateManager.updateTemplate(updateRequest);
      }

      if (savedTemplate) {
        onSave(savedTemplate);
      }
    } catch (error) {
      console.error('Failed to save template:', error);
      alert('Failed to save template. Please try again.');
    }
  };

  const handleColorChange = (colorKey: keyof TemplateColors, value: string) => {
    const rgb = hexToRgb(value);
    if (rgb) {
      setFormData(prev => ({
        ...prev,
        colors: {
          ...prev.colors,
          [colorKey]: [rgb.r, rgb.g, rgb.b] as [number, number, number]
        }
      }));
    }
  };

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const rgbToHex = (rgb: [number, number, number]) => {
    return "#" + rgb.map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: Settings },
    { id: 'colors', label: 'Colors', icon: Palette },
    { id: 'layout', label: 'Layout', icon: Layout },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {isCreating ? 'Create New Template' : `Edit ${template?.name}`}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter template name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter template description"
                />
              </div>
            </div>
          )}

          {activeTab === 'colors' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={rgbToHex(formData.colors.primary)}
                      onChange={(e) => handleColorChange('primary', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <span className="text-sm text-gray-600">
                      rgb({formData.colors.primary.join(', ')})
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Accent Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={rgbToHex(formData.colors.accent)}
                      onChange={(e) => handleColorChange('accent', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <span className="text-sm text-gray-600">
                      rgb({formData.colors.accent.join(', ')})
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dark Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={rgbToHex(formData.colors.dark)}
                      onChange={(e) => handleColorChange('dark', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <span className="text-sm text-gray-600">
                      rgb({formData.colors.dark.join(', ')})
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Light Gray
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={rgbToHex(formData.colors.lightGray)}
                      onChange={(e) => handleColorChange('lightGray', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <span className="text-sm text-gray-600">
                      rgb({formData.colors.lightGray.join(', ')})
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Medium Gray
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={rgbToHex(formData.colors.mediumGray)}
                      onChange={(e) => handleColorChange('mediumGray', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <span className="text-sm text-gray-600">
                      rgb({formData.colors.mediumGray.join(', ')})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'layout' && (
            <div className="space-y-6">
              {/* Paper Size Section */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Paper Configuration
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Paper Size
                    </label>
                    <select
                      value={formData.layout.paperSize}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        layout: { ...prev.layout, paperSize: e.target.value as PaperSize }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {Object.entries(PAPER_SIZES).map(([key, size]) => (
                        <option key={key} value={key}>
                          {size.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Orientation
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="portrait"
                          checked={formData.layout.orientation === 'portrait'}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            layout: { ...prev.layout, orientation: e.target.value as PaperOrientation }
                          }))}
                          className="mr-2"
                        />
                        <FileText className="w-4 h-4 mr-1" />
                        Portrait
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="landscape"
                          checked={formData.layout.orientation === 'landscape'}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            layout: { ...prev.layout, orientation: e.target.value as PaperOrientation }
                          }))}
                          className="mr-2"
                        />
                        <RotateCcw className="w-4 h-4 mr-1" />
                        Landscape
                      </label>
                    </div>
                  </div>
                </div>

                {/* Paper Dimensions Display */}
                <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
                  <div className="text-sm text-blue-800">
                    <strong>Paper Dimensions:</strong> {
                      (() => {
                        const dims = getPaperDimensions(formData.layout.paperSize, formData.layout.orientation);
                        return `${dims.width} × ${dims.height} mm`;
                      })()
                    }
                  </div>
                </div>
              </div>

              {/* Layout Settings */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Layout className="w-5 h-5 mr-2" />
                  Layout Settings
                </h3>
                <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Margin (mm)
                  </label>
                  <input
                    type="number"
                    value={formData.layout.margin}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      layout: { ...prev.layout, margin: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    max="20"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Header Height (mm)
                  </label>
                  <input
                    type="number"
                    value={formData.layout.headerHeight}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      layout: { ...prev.layout, headerHeight: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="20"
                    max="60"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Section Spacing (mm)
                  </label>
                  <input
                    type="number"
                    value={formData.layout.sectionSpacing}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      layout: { ...prev.layout, sectionSpacing: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    max="20"
                  />
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Font Sizes</h4>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Header Font Size
                    </label>
                    <input
                      type="number"
                      value={formData.layout.fontSize.header}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        layout: {
                          ...prev.layout,
                          fontSize: { ...prev.layout.fontSize, header: Number(e.target.value) }
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="12"
                      max="30"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subheader Font Size
                    </label>
                    <input
                      type="number"
                      value={formData.layout.fontSize.subheader}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        layout: {
                          ...prev.layout,
                          fontSize: { ...prev.layout.fontSize, subheader: Number(e.target.value) }
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="8"
                      max="20"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Body Font Size
                    </label>
                    <input
                      type="number"
                      value={formData.layout.fontSize.body}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        layout: {
                          ...prev.layout,
                          fontSize: { ...prev.layout.fontSize, body: Number(e.target.value) }
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="6"
                      max="16"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Small Font Size
                    </label>
                    <input
                      type="number"
                      value={formData.layout.fontSize.small}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        layout: {
                          ...prev.layout,
                          fontSize: { ...prev.layout.fontSize, small: Number(e.target.value) }
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="4"
                      max="12"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Display Options</h4>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.showLogo}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showLogo: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show Logo</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.showWatermark}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showWatermark: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show Watermark</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.showGSTBreakdown}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showGSTBreakdown: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show GST Breakdown</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.showBankDetails}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showBankDetails: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show Bank Details</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.showTermsAndConditions}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showTermsAndConditions: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show Terms & Conditions</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.settings.compactMode}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, compactMode: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Compact Mode</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Format Settings</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency Symbol
                    </label>
                    <input
                      type="text"
                      value={formData.settings.currency}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, currency: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="₹"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date Format
                    </label>
                    <select
                      value={formData.settings.dateFormat}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, dateFormat: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      <option value="DD-MM-YYYY">DD-MM-YYYY</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number Format
                    </label>
                    <select
                      value={formData.settings.numberFormat}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, numberFormat: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="en-IN">Indian (1,23,456.78)</option>
                      <option value="en-US">US (123,456.78)</option>
                      <option value="en-GB">UK (123,456.78)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            {isCreating ? 'Create Template' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};