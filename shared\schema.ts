import { mysqlTable, varchar, int, boolean, decimal, timestamp, date, json } from "drizzle-orm/mysql-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table
export const users = mysqlTable("users", {
  id: int("id").primaryKey().autoincrement(),
  username: varchar("username", { length: 255 }).notNull().unique(),
  password: varchar("password", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  role: varchar("role", { length: 50 }).notNull().default("user"), // admin, manager, user
  createdAt: timestamp("created_at").defaultNow(),
});

// Suppliers table for metal procurement
export const suppliers = mysqlTable("suppliers", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 255 }).notNull(),
  contactPerson: varchar("contact_person", { length: 255 }),
  phone: varchar("phone", { length: 20 }),
  email: varchar("email", { length: 255 }),
  address: varchar("address", { length: 500 }),
  gstin: varchar("gstin", { length: 15 }),
  supplierType: varchar("supplier_type", { length: 50 }).notNull(), // bullion_dealer, refiner, bank
  createdAt: timestamp("created_at").defaultNow(),
});

// Metal procurement records
export const metalProcurement = mysqlTable("metal_procurement", {
  id: int("id").primaryKey().autoincrement(),
  supplierId: int("supplier_id").references(() => suppliers.id).notNull(),
  metalType: varchar("metal_type", { length: 50 }).notNull(), // gold, silver
  purity: varchar("purity", { length: 10 }).notNull(), // 999, 995, 916, 925
  weight: decimal("weight", { precision: 10, scale: 3 }).notNull(),
  purchaseRate: decimal("purchase_rate", { precision: 10, scale: 2 }).notNull(),
  totalAmount: decimal("total_amount", { precision: 12, scale: 2 }).notNull(),
  invoiceNumber: varchar("invoice_number", { length: 100 }).notNull(),
  invoiceDate: date("invoice_date").notNull(),
  hsnCode: varchar("hsn_code", { length: 20 }).notNull(),
  gstAmount: decimal("gst_amount", { precision: 10, scale: 2 }),
  createdAt: timestamp("created_at").defaultNow(),
});

// Customers table
export const customers = mysqlTable("customers", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 255 }).notNull(),
  contactPerson: varchar("contact_person", { length: 255 }),
  phone: varchar("phone", { length: 20 }),
  email: varchar("email", { length: 255 }),
  address: varchar("address", { length: 500 }),
  gstin: varchar("gstin", { length: 15 }),
  pan: varchar("pan", { length: 10 }),
  stateCode: varchar("state_code", { length: 5 }),
  creditLimit: decimal("credit_limit", { precision: 12, scale: 2 }).default("0"),
  outstandingAmount: decimal("outstanding_amount", { precision: 12, scale: 2 }).default("0"),
  discountType: varchar("discount_type", { length: 20 }).default("none"), // fixed, percentage, none
  discountValue: decimal("discount_value", { precision: 5, scale: 2 }).default("0"),
  priceType: varchar("price_type", { length: 20 }).default("standard"), // standard, special
  createdAt: timestamp("created_at").defaultNow(),
});

// Metal rates management
export const metalRates = mysqlTable("metal_rates", {
  id: int("id").primaryKey().autoincrement(),
  metalType: varchar("metal_type", { length: 50 }).notNull(), // gold, silver
  purity: varchar("purity", { length: 10 }).notNull(),
  ratePerGram: decimal("rate_per_gram", { precision: 10, scale: 2 }).notNull(),
  rateDate: date("rate_date").notNull(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Inventory items
export const inventoryItems = mysqlTable("inventory_items", {
  id: int("id").primaryKey().autoincrement(),
  itemCode: varchar("item_code", { length: 100 }).notNull().unique(),
  itemName: varchar("item_name", { length: 255 }).notNull(),
  designName: varchar("design_name", { length: 255 }),
  metalType: varchar("metal_type", { length: 50 }).notNull(),
  purity: varchar("purity", { length: 10 }).notNull(),
  purityPercentage: decimal("purity_percentage", { precision: 5, scale: 2 }).notNull(), // e.g., 96 for 96%
  grossWeight: decimal("gross_weight", { precision: 10, scale: 3 }).notNull(),
  netWeight: decimal("net_weight", { precision: 10, scale: 3 }).notNull(),
  fineWeight: decimal("fine_weight", { precision: 10, scale: 3 }).notNull(), // Net Weight × (Purity % ÷ 100)
  stoneWeight: decimal("stone_weight", { precision: 10, scale: 3 }).default("0"),
  stoneAmount: decimal("stone_amount", { precision: 10, scale: 2 }).default("0"), // Stone price
  wastagePercentage: decimal("wastage_percentage", { precision: 5, scale: 2 }).default("0"),
  makingChargesRate: decimal("making_charges_rate", { precision: 10, scale: 2 }).default("0"), // Rate per gram
  makingCharges: decimal("making_charges", { precision: 10, scale: 2 }).default("0"), // Net Weight × MC Rate
  stoneCharges: decimal("stone_charges", { precision: 10, scale: 2 }).default("0"),
  valueAdditionPercentage: decimal("value_addition_percentage", { precision: 5, scale: 2 }).default("0"),
  hsnCode: varchar("hsn_code", { length: 20 }).notNull(),
  barcode: varchar("barcode", { length: 100 }),
  quantity: int("quantity").default(1),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Invoices
export const invoices = mysqlTable("invoices", {
  id: int("id").primaryKey().autoincrement(),
  invoiceNumber: varchar("invoice_number", { length: 100 }).notNull().unique(),
  customerId: int("customer_id").references(() => customers.id).notNull(),
  invoiceDate: date("invoice_date").notNull(),
  dueDate: date("due_date"),
  subtotal: decimal("subtotal", { precision: 12, scale: 2 }).notNull(),
  cgstAmount: decimal("cgst_amount", { precision: 10, scale: 2 }).default("0"),
  sgstAmount: decimal("sgst_amount", { precision: 10, scale: 2 }).default("0"),
  igstAmount: decimal("igst_amount", { precision: 10, scale: 2 }).default("0"),
  discountAmount: decimal("discount_amount", { precision: 10, scale: 2 }).default("0"),
  totalAmount: decimal("total_amount", { precision: 12, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 12, scale: 2 }).default("0"),
  balanceAmount: decimal("balance_amount", { precision: 12, scale: 2 }).notNull(),
  status: varchar("status", { length: 20 }).notNull().default("pending"), // pending, paid, overdue
  paymentTerms: varchar("payment_terms", { length: 500 }),
  notes: varchar("notes", { length: 1000 }),
  ewayBillNumber: varchar("eway_bill_number", { length: 50 }),
  createdAt: timestamp("created_at").defaultNow(),
});

// Invoice items
export const invoiceItems = mysqlTable("invoice_items", {
  id: int("id").primaryKey().autoincrement(),
  invoiceId: int("invoice_id").references(() => invoices.id).notNull(),
  itemId: int("item_id").references(() => inventoryItems.id).notNull(),
  quantity: int("quantity").notNull().default(1),
  grossWeight: decimal("gross_weight", { precision: 10, scale: 3 }).notNull(),
  netWeight: decimal("net_weight", { precision: 10, scale: 3 }).notNull(),
  fineWeight: decimal("fine_weight", { precision: 10, scale: 3 }).notNull(),
  purityPercentage: decimal("purity_percentage", { precision: 5, scale: 2 }).notNull(),
  stoneWeight: decimal("stone_weight", { precision: 10, scale: 3 }).default("0"),
  stoneAmount: decimal("stone_amount", { precision: 10, scale: 2 }).default("0"),
  wastagePercentage: decimal("wastage_percentage", { precision: 5, scale: 2 }).default("0"),
  ratePerGram: decimal("rate_per_gram", { precision: 10, scale: 2 }).notNull(), // Gold rate
  goldValue: decimal("gold_value", { precision: 12, scale: 2 }).notNull(), // Fine Weight × Gold Rate
  makingChargesRate: decimal("making_charges_rate", { precision: 10, scale: 2 }).default("0"),
  makingCharges: decimal("making_charges", { precision: 10, scale: 2 }).default("0"), // Net Weight × MC Rate
  stoneCharges: decimal("stone_charges", { precision: 10, scale: 2 }).default("0"),
  itemTotal: decimal("item_total", { precision: 12, scale: 2 }).notNull(), // Gold Value + MC + Stone Amount
  taxableAmount: decimal("taxable_amount", { precision: 12, scale: 2 }).notNull(), // Item Total ÷ 1.03
  cgstAmount: decimal("cgst_amount", { precision: 10, scale: 2 }).default("0"), // Taxable Amount × 0.015
  sgstAmount: decimal("sgst_amount", { precision: 10, scale: 2 }).default("0"), // Taxable Amount × 0.015
  totalAmount: decimal("total_amount", { precision: 12, scale: 2 }).notNull(), // Taxable Amount + CGST + SGST
});

// Payments
export const payments = mysqlTable("payments", {
  id: int("id").primaryKey().autoincrement(),
  customerId: int("customer_id").references(() => customers.id).notNull(),
  invoiceId: int("invoice_id").references(() => invoices.id),
  paymentDate: date("payment_date").notNull(),
  amount: decimal("amount", { precision: 12, scale: 2 }).notNull(),
  paymentMode: varchar("payment_mode", { length: 50 }).notNull(), // cash, bank, upi, rtgs, cheque
  referenceNumber: varchar("reference_number", { length: 100 }),
  notes: varchar("notes", { length: 500 }),
  createdAt: timestamp("created_at").defaultNow(),
});

// Settings
export const settings = mysqlTable("settings", {
  id: int("id").primaryKey().autoincrement(),
  category: varchar("category", { length: 50 }).notNull(), // general, company, appearance, etc.
  key: varchar("key", { length: 100 }).notNull(),
  value: json("value"), // Store as JSON for flexibility
  description: varchar("description", { length: 500 }),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Templates table for invoice templates
export const templates = mysqlTable("templates", {
  id: varchar("id", { length: 50 }).primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  description: varchar("description", { length: 500 }),
  category: varchar("category", { length: 20 }).notNull().default("custom"), // modern, classic, minimal, luxury, custom
  colors: json("colors").notNull(), // TemplateColors object
  layout: json("layout").notNull(), // TemplateLayout object
  settings: json("settings").notNull(), // TemplateSettings object
  customFields: json("custom_fields"), // Additional custom fields
  isDefault: boolean("is_default").notNull().default(false),
  isActive: boolean("is_active").notNull().default(true),
  createdBy: varchar("created_by", { length: 50 }).notNull(), // user ID or 'system'
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow().onUpdateNow(),
});

// Relations
export const suppliersRelations = relations(suppliers, ({ many }) => ({
  procurements: many(metalProcurement),
}));

export const metalProcurementRelations = relations(metalProcurement, ({ one }) => ({
  supplier: one(suppliers, {
    fields: [metalProcurement.supplierId],
    references: [suppliers.id],
  }),
}));

export const customersRelations = relations(customers, ({ many }) => ({
  invoices: many(invoices),
  payments: many(payments),
}));

export const invoicesRelations = relations(invoices, ({ one, many }) => ({
  customer: one(customers, {
    fields: [invoices.customerId],
    references: [customers.id],
  }),
  items: many(invoiceItems),
  payments: many(payments),
}));

export const invoiceItemsRelations = relations(invoiceItems, ({ one }) => ({
  invoice: one(invoices, {
    fields: [invoiceItems.invoiceId],
    references: [invoices.id],
  }),
  item: one(inventoryItems, {
    fields: [invoiceItems.itemId],
    references: [inventoryItems.id],
  }),
}));

export const paymentsRelations = relations(payments, ({ one }) => ({
  customer: one(customers, {
    fields: [payments.customerId],
    references: [customers.id],
  }),
  invoice: one(invoices, {
    fields: [payments.invoiceId],
    references: [invoices.id],
  }),
}));

// Schemas
export const insertUserSchema = createInsertSchema(users).omit({ id: true, createdAt: true });
export const insertSupplierSchema = createInsertSchema(suppliers).omit({ id: true, createdAt: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true, createdAt: true });
export const insertMetalProcurementSchema = createInsertSchema(metalProcurement).omit({ id: true, createdAt: true }).extend({
  invoiceDate: z.string().transform((str) => new Date(str)),
});
export const insertMetalRateSchema = createInsertSchema(metalRates).omit({ id: true, createdAt: true }).extend({
  metalType: z.enum(["gold", "silver", "platinum"], { required_error: "Metal type is required" }),
  purity: z.string().min(1, "Purity is required"),
  ratePerGram: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, "Rate per gram must be a positive number"),
  rateDate: z.string().transform((str) => new Date(str)),
});
export const insertInventoryItemSchema = createInsertSchema(inventoryItems).omit({ id: true, createdAt: true, fineWeight: true, makingCharges: true }).extend({
  itemCode: z.string().min(1, "Item code is required").max(20, "Item code must be 20 characters or less"),
  itemName: z.string().min(1, "Item name is required").max(100, "Item name must be 100 characters or less"),
  designName: z.string().min(1, "Design name is required").max(100, "Design name must be 100 characters or less"),
  metalType: z.enum(["gold", "silver", "platinum"], { required_error: "Metal type is required" }),
  purity: z.string().min(1, "Purity is required"),
  purityPercentage: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0 && parseFloat(val) <= 100, "Purity percentage must be between 0 and 100"),
  grossWeight: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, "Gross weight must be a positive number"),
  netWeight: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, "Net weight must be a positive number"),
  stoneWeight: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), "Stone weight must be a non-negative number").optional(),
  stoneAmount: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), "Stone amount must be a non-negative number").optional(),
  wastagePercentage: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0 && parseFloat(val) <= 100), "Wastage percentage must be between 0 and 100").optional(),
  makingChargesRate: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), "Making charges rate must be a non-negative number").optional(),
  stoneCharges: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), "Stone charges must be a non-negative number").optional(),
  valueAdditionPercentage: z.string().refine((val) => !val || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0 && parseFloat(val) <= 100), "Value addition percentage must be between 0 and 100").optional(),
  hsnCode: z.string().min(1, "HSN code is required").max(10, "HSN code must be 10 characters or less"),
  barcode: z.string().max(50, "Barcode must be 50 characters or less").optional(),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
});
export const insertInvoiceSchema = createInsertSchema(invoices).omit({ id: true, createdAt: true }).extend({
  invoiceDate: z.string().transform((str) => new Date(str)),
  dueDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
});
export const insertInvoiceItemSchema = createInsertSchema(invoiceItems).omit({ id: true });
export const insertPaymentSchema = createInsertSchema(payments).omit({ id: true, createdAt: true }).extend({
  paymentDate: z.string().transform((str) => new Date(str)),
});
export const insertSettingSchema = createInsertSchema(settings).omit({ id: true, updatedAt: true });
export const insertTemplateSchema = createInsertSchema(templates).omit({ createdAt: true, updatedAt: true });

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Supplier = typeof suppliers.$inferSelect;
export type InsertSupplier = z.infer<typeof insertSupplierSchema>;
export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type MetalProcurement = typeof metalProcurement.$inferSelect;
export type InsertMetalProcurement = z.infer<typeof insertMetalProcurementSchema>;
export type MetalRate = typeof metalRates.$inferSelect;
export type InsertMetalRate = z.infer<typeof insertMetalRateSchema>;
export type InventoryItem = typeof inventoryItems.$inferSelect;
export type InsertInventoryItem = z.infer<typeof insertInventoryItemSchema>;
export type Invoice = typeof invoices.$inferSelect;
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type InvoiceItem = typeof invoiceItems.$inferSelect;
export type InsertInvoiceItem = z.infer<typeof insertInvoiceItemSchema>;
export type Payment = typeof payments.$inferSelect;
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Setting = typeof settings.$inferSelect;
export type InsertSetting = z.infer<typeof insertSettingSchema>;
export type Template = typeof templates.$inferSelect;
export type InsertTemplate = z.infer<typeof insertTemplateSchema>;
