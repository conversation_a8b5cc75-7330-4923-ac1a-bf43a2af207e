import { db } from "./db";
import { invoices, inventoryItems, customers, metalRates } from "@shared/schema";
import { sql } from "drizzle-orm";

async function checkDatabaseData() {
  console.log("🔍 Checking database data...\n");

  try {
    // Check invoices
    const invoiceCount = await db.select({ count: sql`COUNT(*)` }).from(invoices);
    console.log(`📄 Invoices: ${invoiceCount[0]?.count || 0} records`);

    // Check recent invoices
    const recentInvoices = await db.select().from(invoices).limit(3);
    if (recentInvoices.length > 0) {
      console.log("   Recent invoices:");
      recentInvoices.forEach(inv => {
        console.log(`   - ${inv.invoiceNumber}: ₹${inv.totalAmount} (${inv.invoiceDate})`);
      });
    }

    // Check inventory
    const inventoryCount = await db.select({ count: sql`COUNT(*)` }).from(inventoryItems);
    console.log(`\n📦 Inventory Items: ${inventoryCount[0]?.count || 0} records`);

    // Check inventory value
    const inventoryValue = await db.select({ 
      value: sql`COALESCE(SUM(${inventoryItems.makingCharges} + ${inventoryItems.stoneCharges}), 0)`
    }).from(inventoryItems);
    console.log(`   Total Value: ₹${inventoryValue[0]?.value || 0}`);

    // Check customers
    const customerCount = await db.select({ count: sql`COUNT(*)` }).from(customers);
    console.log(`\n👥 Customers: ${customerCount[0]?.count || 0} records`);

    // Check metal rates
    const rateCount = await db.select({ count: sql`COUNT(*)` }).from(metalRates);
    console.log(`\n💰 Metal Rates: ${rateCount[0]?.count || 0} records`);

    // Check current rates
    const currentRates = await db.select().from(metalRates).limit(5);
    if (currentRates.length > 0) {
      console.log("   Current rates:");
      currentRates.forEach(rate => {
        console.log(`   - ${rate.metalType} ${rate.purity}: ₹${rate.ratePerGram}/g`);
      });
    }

    // Calculate dashboard stats
    const today = new Date().toISOString().split('T')[0];
    
    const todaysSales = await db
      .select({ total: sql`COALESCE(SUM(${invoices.totalAmount}), 0)` })
      .from(invoices)
      .where(sql`DATE(${invoices.invoiceDate}) = ${today}`);

    const pendingPayments = await db
      .select({ total: sql`COALESCE(SUM(${invoices.balanceAmount}), 0)` })
      .from(invoices)
      .where(sql`${invoices.balanceAmount} > 0`);

    console.log(`\n📊 Dashboard Stats:`);
    console.log(`   Today's Sales: ₹${todaysSales[0]?.total || 0}`);
    console.log(`   Pending Payments: ₹${pendingPayments[0]?.total || 0}`);
    console.log(`   Inventory Value: ₹${inventoryValue[0]?.value || 0}`);

  } catch (error) {
    console.error("❌ Error checking data:", error);
  }
}

// Run the check
checkDatabaseData()
  .then(() => {
    console.log("\n✅ Data check complete!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Failed to check data:", error);
    process.exit(1);
  });
