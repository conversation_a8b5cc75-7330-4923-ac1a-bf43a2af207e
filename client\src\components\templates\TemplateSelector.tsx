import React, { useState, useEffect } from 'react';
import { ChevronDown, Palette, <PERSON>, Settings, FileText } from 'lucide-react';
import { templateManager } from '../../lib/template-manager';
import { InvoiceTemplate, PAPER_SIZES } from '../../types/template';

interface TemplateSelectorProps {
  selectedTemplateId?: string;
  onTemplateChange: (templateId: string) => void;
  showPreview?: boolean;
  className?: string;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  selectedTemplateId,
  onTemplateChange,
  showPreview = true,
  className = ''
}) => {
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    if (selectedTemplateId) {
      const template = templateManager.getTemplate(selectedTemplateId);
      setSelectedTemplate(template);
    } else {
      const defaultTemplate = templateManager.getDefaultTemplate();
      setSelectedTemplate(defaultTemplate);
      if (defaultTemplate) {
        onTemplateChange(defaultTemplate.id);
      }
    }
  }, [selectedTemplateId, onTemplateChange]);

  const loadTemplates = () => {
    const allTemplates = templateManager.getAllTemplates();
    setTemplates(allTemplates.filter(t => t.isActive));
  };

  const handleTemplateSelect = (template: InvoiceTemplate) => {
    setSelectedTemplate(template);
    onTemplateChange(template.id);
    setIsOpen(false);
  };

  const getTemplatePreview = (template: InvoiceTemplate) => {
    const { colors } = template;
    return (
      <div className="flex items-center gap-2">
        <div className="flex gap-1">
          <div 
            className="w-3 h-3 rounded-sm border"
            style={{ backgroundColor: `rgb(${colors.primary.join(',')})` }}
          />
          <div 
            className="w-3 h-3 rounded-sm border"
            style={{ backgroundColor: `rgb(${colors.accent.join(',')})` }}
          />
        </div>
        <span className="text-sm">{template.name}</span>
        {template.isDefault && (
          <span className="px-1.5 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded">
            Default
          </span>
        )}
      </div>
    );
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        <Palette className="w-4 h-4 inline mr-1" />
        Invoice Template
      </label>
      
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-left focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400 transition-colors"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {selectedTemplate ? (
                getTemplatePreview(selectedTemplate)
              ) : (
                <span className="text-gray-500">Select a template</span>
              )}
            </div>
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
            {templates.map((template) => (
              <button
                key={template.id}
                type="button"
                onClick={() => handleTemplateSelect(template)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                  selectedTemplate?.id === template.id ? 'bg-blue-50 text-blue-700' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    {getTemplatePreview(template)}
                    {template.description && (
                      <div className="text-xs text-gray-500 mt-1">{template.description}</div>
                    )}
                  </div>
                  {showPreview && (
                    <div className="flex items-center gap-1 ml-2">
                      <span className={`px-2 py-1 text-xs rounded ${
                        template.createdBy === 'system' 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'bg-green-100 text-green-700'
                      }`}>
                        {template.createdBy === 'system' ? 'Built-in' : 'Custom'}
                      </span>
                    </div>
                  )}
                </div>
              </button>
            ))}
            
            {templates.length === 0 && (
              <div className="px-3 py-4 text-center text-gray-500">
                <Palette className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <div className="text-sm">No templates available</div>
              </div>
            )}
          </div>
        )}
      </div>

      {selectedTemplate && showPreview && (
        <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900">Template Preview</h4>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">
                {selectedTemplate.settings.compactMode ? 'Compact' : 'Standard'} Layout
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-gray-600">Colors:</span>
              <div className="flex gap-1 mt-1">
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: `rgb(${selectedTemplate.colors.primary.join(',')})` }}
                  title="Primary"
                />
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: `rgb(${selectedTemplate.colors.accent.join(',')})` }}
                  title="Accent"
                />
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: `rgb(${selectedTemplate.colors.dark.join(',')})` }}
                  title="Dark"
                />
              </div>
            </div>
            
            <div>
              <span className="text-gray-600">Paper & Features:</span>
              <div className="mt-1 space-y-1">
                <div className="flex items-center text-blue-600">
                  <FileText className="w-3 h-3 mr-1" />
                  {PAPER_SIZES[selectedTemplate.layout.paperSize]?.label || selectedTemplate.layout.paperSize.toUpperCase()}
                  {selectedTemplate.layout.orientation === 'landscape' && ' (Landscape)'}
                </div>
                {selectedTemplate.settings.showLogo && (
                  <div className="text-green-600">✓ Logo</div>
                )}
                {selectedTemplate.settings.showBankDetails && (
                  <div className="text-green-600">✓ Bank Details</div>
                )}
                {selectedTemplate.settings.showWatermark && (
                  <div className="text-green-600">✓ Watermark</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};