import {
  users, suppliers, customers, metalProcurement, metalRates, inventoryItems, invoices, invoiceItems, payments, settings, templates,
  type User, type InsertUser, type Supplier, type InsertSupplier, type Customer, type InsertCustomer,
  type MetalProcurement, type InsertMetalProcurement, type MetalRate, type InsertMetalRate,
  type InventoryItem, type InsertInventoryItem, type Invoice, type InsertInvoice,
  type InvoiceItem, type InsertInvoiceItem, type Payment, type InsertPayment,
  type Setting, type InsertSetting, type Template, type InsertTemplate
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, gte, lte, sql } from "drizzle-orm";
import { calculateJewelryValues } from "@shared/jewelry-calculations";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Supplier methods
  getSuppliers(): Promise<Supplier[]>;
  getSupplier(id: number): Promise<Supplier | undefined>;
  createSupplier(supplier: InsertSupplier): Promise<Supplier>;
  updateSupplier(id: number, supplier: Partial<InsertSupplier>): Promise<Supplier | undefined>;
  deleteSupplier(id: number): Promise<boolean>;

  // Customer methods
  getCustomers(): Promise<Customer[]>;
  getCustomer(id: number): Promise<Customer | undefined>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  deleteCustomer(id: number): Promise<boolean>;

  // Metal procurement methods
  getMetalProcurements(): Promise<MetalProcurement[]>;
  getMetalProcurement(id: number): Promise<MetalProcurement | undefined>;
  createMetalProcurement(procurement: InsertMetalProcurement): Promise<MetalProcurement>;
  updateMetalProcurement(id: number, procurement: Partial<InsertMetalProcurement>): Promise<MetalProcurement | undefined>;
  deleteMetalProcurement(id: number): Promise<boolean>;

  // Metal rates methods
  getMetalRates(): Promise<MetalRate[]>;
  getCurrentMetalRates(): Promise<MetalRate[]>;
  createMetalRate(rate: InsertMetalRate): Promise<MetalRate>;
  updateMetalRate(id: number, rate: Partial<InsertMetalRate>): Promise<MetalRate | undefined>;
  deleteMetalRate(id: number): Promise<boolean>;

  // Inventory methods
  getInventoryItems(): Promise<InventoryItem[]>;
  getInventoryItem(id: number): Promise<InventoryItem | undefined>;
  createInventoryItem(item: InsertInventoryItem): Promise<InventoryItem>;
  updateInventoryItem(id: number, item: Partial<InsertInventoryItem>): Promise<InventoryItem | undefined>;
  deleteInventoryItem(id: number): Promise<boolean>;

  // Invoice methods
  getInvoices(): Promise<Invoice[]>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  deleteInvoice(id: number): Promise<boolean>;

  // Invoice items methods
  getInvoiceItems(invoiceId: number): Promise<InvoiceItem[]>;
  createInvoiceItem(item: InsertInvoiceItem): Promise<InvoiceItem>;
  updateInvoiceItem(id: number, item: Partial<InsertInvoiceItem>): Promise<InvoiceItem | undefined>;
  deleteInvoiceItem(id: number): Promise<boolean>;

  // Payment methods
  getPayments(): Promise<Payment[]>;
  getCustomerPayments(customerId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  updatePayment(id: number, payment: Partial<InsertPayment>): Promise<Payment | undefined>;
  deletePayment(id: number): Promise<boolean>;

  // Settings methods
  getSettings(category?: string): Promise<Setting[]>;
  getSetting(category: string, key: string): Promise<Setting | undefined>;
  setSetting(setting: InsertSetting): Promise<Setting>;
  deleteSetting(category: string, key: string): Promise<boolean>;

  // Template methods
  getTemplates(): Promise<Template[]>;
  getTemplate(id: string): Promise<Template | undefined>;
  createTemplate(template: InsertTemplate): Promise<Template>;
  updateTemplate(id: string, template: Partial<InsertTemplate>): Promise<Template | undefined>;
  deleteTemplate(id: string): Promise<boolean>;
  getTemplateStats(): Promise<{ total: number; active: number; custom: number; system: number }>;

  // Dashboard methods
  getDashboardStats(): Promise<{
    todaysSales: string;
    inventoryValue: string;
    inventoryCount: number;
    pendingPayments: string;
    overdueCount: number;
    lowStockCount: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await db.insert(users).values(insertUser);
    const [user] = await db.select().from(users).where(eq(users.id, result.insertId));
    return user;
  }

  async getSuppliers(): Promise<Supplier[]> {
    return await db.select().from(suppliers).orderBy(desc(suppliers.createdAt));
  }

  async getSupplier(id: number): Promise<Supplier | undefined> {
    const [supplier] = await db.select().from(suppliers).where(eq(suppliers.id, id));
    return supplier || undefined;
  }

  async createSupplier(supplier: InsertSupplier): Promise<Supplier> {
    const result = await db.insert(suppliers).values(supplier);
    const [newSupplier] = await db.select().from(suppliers).where(eq(suppliers.id, result.insertId));
    return newSupplier;
  }

  async updateSupplier(id: number, supplier: Partial<InsertSupplier>): Promise<Supplier | undefined> {
    await db.update(suppliers).set(supplier).where(eq(suppliers.id, id));
    const [updated] = await db.select().from(suppliers).where(eq(suppliers.id, id));
    return updated || undefined;
  }

  async deleteSupplier(id: number): Promise<boolean> {
    const result = await db.delete(suppliers).where(eq(suppliers.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getCustomers(): Promise<Customer[]> {
    return await db.select().from(customers).orderBy(desc(customers.createdAt));
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    const [customer] = await db.select().from(customers).where(eq(customers.id, id));
    return customer || undefined;
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    const result = await db.insert(customers).values(customer);
    const [newCustomer] = await db.select().from(customers).where(eq(customers.id, result.insertId));
    return newCustomer;
  }

  async updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined> {
    await db.update(customers).set(customer).where(eq(customers.id, id));
    const [updated] = await db.select().from(customers).where(eq(customers.id, id));
    return updated || undefined;
  }

  async deleteCustomer(id: number): Promise<boolean> {
    const result = await db.delete(customers).where(eq(customers.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getMetalProcurements(): Promise<MetalProcurement[]> {
    return await db.select().from(metalProcurement).orderBy(desc(metalProcurement.createdAt));
  }

  async getMetalProcurement(id: number): Promise<MetalProcurement | undefined> {
    const [procurement] = await db.select().from(metalProcurement).where(eq(metalProcurement.id, id));
    return procurement || undefined;
  }

  async createMetalProcurement(procurement: InsertMetalProcurement): Promise<MetalProcurement> {
    const result = await db.insert(metalProcurement).values(procurement);
    const [newProcurement] = await db.select().from(metalProcurement).where(eq(metalProcurement.id, result.insertId));
    return newProcurement;
  }

  async updateMetalProcurement(id: number, procurement: Partial<InsertMetalProcurement>): Promise<MetalProcurement | undefined> {
    await db.update(metalProcurement).set(procurement).where(eq(metalProcurement.id, id));
    const [updated] = await db.select().from(metalProcurement).where(eq(metalProcurement.id, id));
    return updated || undefined;
  }

  async deleteMetalProcurement(id: number): Promise<boolean> {
    const result = await db.delete(metalProcurement).where(eq(metalProcurement.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getMetalRates(): Promise<MetalRate[]> {
    return await db.select().from(metalRates).orderBy(desc(metalRates.rateDate));
  }

  async getCurrentMetalRates(): Promise<MetalRate[]> {
    return await db.select().from(metalRates).where(eq(metalRates.isActive, true));
  }

  async createMetalRate(rate: InsertMetalRate): Promise<MetalRate> {
    // First, deactivate any existing rates for the same metal type and purity
    if (rate.isActive !== false) { // Only deactivate if we're creating an active rate
      await db.update(metalRates)
        .set({ isActive: false })
        .where(
          and(
            eq(metalRates.metalType, rate.metalType),
            eq(metalRates.purity, rate.purity),
            eq(metalRates.isActive, true)
          )
        );
    }

    // Ensure the new rate is marked as active if not explicitly set to false
    const rateToInsert = {
      ...rate,
      isActive: rate.isActive !== false // Default to true unless explicitly set to false
    };

    const result = await db.insert(metalRates).values(rateToInsert);

    // Handle MySQL2 result structure properly
    let insertId: number;
    if (Array.isArray(result) && result[0] && typeof result[0].insertId === 'number') {
      insertId = result[0].insertId;
    } else if (typeof result.insertId === 'number') {
      insertId = result.insertId;
    } else {
      throw new Error('Failed to get insert ID from database result');
    }

    // Retrieve the created rate
    const [newRate] = await db.select().from(metalRates).where(eq(metalRates.id, insertId));

    if (!newRate) {
      throw new Error(`Failed to retrieve created metal rate with ID ${insertId}`);
    }

    return newRate;
  }

  async updateMetalRate(id: number, rate: Partial<InsertMetalRate>): Promise<MetalRate | undefined> {
    const result = await db.update(metalRates).set(rate).where(eq(metalRates.id, id));

    // Check if any rows were affected
    if ((result.rowCount || 0) === 0) {
      return undefined;
    }

    const [updated] = await db.select().from(metalRates).where(eq(metalRates.id, id));
    return updated || undefined;
  }

  async deleteMetalRate(id: number): Promise<boolean> {
    const result = await db.delete(metalRates).where(eq(metalRates.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getInventoryItems(): Promise<InventoryItem[]> {
    return await db.select().from(inventoryItems).where(eq(inventoryItems.isActive, true)).orderBy(desc(inventoryItems.createdAt));
  }

  async getInventoryItem(id: number): Promise<InventoryItem | undefined> {
    const [item] = await db.select().from(inventoryItems).where(eq(inventoryItems.id, id));
    return item || undefined;
  }

  async generateUniqueItemCode(metalType: string, prefix?: string): Promise<string> {
    // Create a prefix based on metal type if not provided
    const codePrefix = prefix || (metalType === 'gold' ? 'G' : metalType === 'silver' ? 'S' : 'J');

    // Get existing item codes with this prefix
    const existingItems = await db.select({ itemCode: inventoryItems.itemCode })
      .from(inventoryItems)
      .where(sql`${inventoryItems.itemCode} LIKE ${codePrefix + '%'}`);

    const existingCodes = existingItems.map((item: any) => item.itemCode);

    // Find the next available number
    let counter = 1;
    let newCode: string;

    do {
      newCode = `${codePrefix}${counter.toString().padStart(3, '0')}`;
      counter++;
    } while (existingCodes.includes(newCode));

    return newCode;
  }

  async createInventoryItem(item: InsertInventoryItem): Promise<InventoryItem> {
    // Auto-generate item code if not provided or if it's empty
    if (!item.itemCode || item.itemCode.trim() === '') {
      item.itemCode = await this.generateUniqueItemCode(item.metalType);
    } else {
      // Check if the provided code already exists
      const existingItem = await db.select({ id: inventoryItems.id })
        .from(inventoryItems)
        .where(eq(inventoryItems.itemCode, item.itemCode))
        .limit(1);

      if (existingItem.length > 0) {
        // Generate a unique code if the provided one already exists
        item.itemCode = await this.generateUniqueItemCode(item.metalType);
      }
    }

    // Calculate fine weight and making charges automatically
    const grossWeight = parseFloat(item.grossWeight as string);
    const stoneWeight = parseFloat((item.stoneWeight as string) || "0");
    const purityPercentage = parseFloat((item.purityPercentage as string) || "96");
    const makingChargesRate = parseFloat((item.makingChargesRate as string) || "0");

    const netWeight = grossWeight - stoneWeight;
    const fineWeight = netWeight * (purityPercentage / 100);
    const makingCharges = netWeight * makingChargesRate;

    // Update the item with calculated values
    const itemWithCalculations = {
      ...item,
      netWeight: netWeight.toString(),
      fineWeight: fineWeight.toString(),
      makingCharges: makingCharges.toString(),
    };

    try {
      const result = await db.insert(inventoryItems).values(itemWithCalculations);

      // Handle MySQL2 result structure properly
      let insertId: number;
      if (Array.isArray(result) && result[0] && typeof result[0].insertId === 'number') {
        insertId = result[0].insertId;
      } else if (typeof result.insertId === 'number') {
        insertId = result.insertId;
      } else {
        throw new Error('Failed to get insert ID from database result');
      }

      // Retrieve the created item
      const [newItem] = await db.select().from(inventoryItems).where(eq(inventoryItems.id, insertId));

      if (!newItem) {
        throw new Error(`Failed to retrieve created inventory item with ID ${insertId}`);
      }

      return newItem;
    } catch (error) {
      console.error('Error in createInventoryItem:', error);
      throw error;
    }
  }

  async updateInventoryItem(id: number, item: Partial<InsertInventoryItem>): Promise<InventoryItem | undefined> {
    // Get the current item to merge with updates
    const [currentItem] = await db.select().from(inventoryItems).where(eq(inventoryItems.id, id));
    if (!currentItem) return undefined;

    // Merge current values with updates for calculations
    const mergedItem = { ...currentItem, ...item };

    // Recalculate if weight or purity fields are being updated
    if (item.grossWeight || item.stoneWeight || item.purityPercentage || item.makingChargesRate) {
      const grossWeight = parseFloat(mergedItem.grossWeight as string);
      const stoneWeight = parseFloat((mergedItem.stoneWeight as string) || "0");
      const purityPercentage = parseFloat((mergedItem.purityPercentage as string) || "96");
      const makingChargesRate = parseFloat((mergedItem.makingChargesRate as string) || "0");

      const netWeight = grossWeight - stoneWeight;
      const fineWeight = netWeight * (purityPercentage / 100);
      const makingCharges = netWeight * makingChargesRate;

      // Update the item with calculated values
      item = {
        ...item,
        netWeight: netWeight.toString(),
        makingCharges: makingCharges.toString(),
      } as any;
    }

    await db.update(inventoryItems).set(item).where(eq(inventoryItems.id, id));
    const [updated] = await db.select().from(inventoryItems).where(eq(inventoryItems.id, id));
    return updated || undefined;
  }

  async deleteInventoryItem(id: number): Promise<boolean> {
    try {
      // First check if the item exists and is active
      const [existingItem] = await db.select().from(inventoryItems)
        .where(and(eq(inventoryItems.id, id), eq(inventoryItems.isActive, true)));

      if (!existingItem) {
        console.log(`Delete failed: Item ${id} not found or already inactive`);
        return false; // Item doesn't exist or is already inactive
      }

      console.log(`Deleting item ${id}: ${existingItem.itemCode}`);

      // Soft delete the item
      await db.update(inventoryItems)
        .set({ isActive: false })
        .where(eq(inventoryItems.id, id));

      // Verify the deletion by checking if the item is now inactive
      const [updatedItem] = await db.select().from(inventoryItems)
        .where(eq(inventoryItems.id, id));

      const success = updatedItem && !updatedItem.isActive;
      console.log(`Delete verification: Item ${id} isActive = ${updatedItem?.isActive}, success = ${success}`);

      return success;
    } catch (error) {
      console.error(`Error in deleteInventoryItem(${id}):`, error);
      return false;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    return await db.select().from(invoices).orderBy(desc(invoices.createdAt));
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice || undefined;
  }

  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    const result = await db.insert(invoices).values(invoice);
    const insertId = result[0]?.insertId;

    if (!insertId) {
      throw new Error('Failed to get insert ID from database');
    }

    const [newInvoice] = await db.select().from(invoices).where(eq(invoices.id, insertId));

    if (!newInvoice) {
      throw new Error('Failed to retrieve created invoice');
    }

    return newInvoice;
  }

  async updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    await db.update(invoices).set(invoice).where(eq(invoices.id, id));
    const [updated] = await db.select().from(invoices).where(eq(invoices.id, id));
    return updated || undefined;
  }

  async deleteInvoice(id: number): Promise<boolean> {
    const result = await db.delete(invoices).where(eq(invoices.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getInvoiceItems(invoiceId: number): Promise<InvoiceItem[]> {
    return await db.select().from(invoiceItems).where(eq(invoiceItems.invoiceId, invoiceId));
  }

  async createInvoiceItem(item: InsertInvoiceItem): Promise<InvoiceItem> {
    const result = await db.insert(invoiceItems).values(item);
    const [newItem] = await db.select().from(invoiceItems).where(eq(invoiceItems.id, result.insertId));
    return newItem;
  }

  async updateInvoiceItem(id: number, item: Partial<InsertInvoiceItem>): Promise<InvoiceItem | undefined> {
    await db.update(invoiceItems).set(item).where(eq(invoiceItems.id, id));
    const [updated] = await db.select().from(invoiceItems).where(eq(invoiceItems.id, id));
    return updated || undefined;
  }

  async deleteInvoiceItem(id: number): Promise<boolean> {
    const result = await db.delete(invoiceItems).where(eq(invoiceItems.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getPayments(): Promise<Payment[]> {
    return await db.select().from(payments).orderBy(desc(payments.createdAt));
  }

  async getCustomerPayments(customerId: number): Promise<Payment[]> {
    return await db.select().from(payments).where(eq(payments.customerId, customerId)).orderBy(desc(payments.createdAt));
  }

  async createPayment(payment: InsertPayment): Promise<Payment> {
    const result = await db.insert(payments).values(payment);
    const [newPayment] = await db.select().from(payments).where(eq(payments.id, result.insertId));
    return newPayment;
  }

  async updatePayment(id: number, payment: Partial<InsertPayment>): Promise<Payment | undefined> {
    await db.update(payments).set(payment).where(eq(payments.id, id));
    const [updated] = await db.select().from(payments).where(eq(payments.id, id));
    return updated || undefined;
  }

  async deletePayment(id: number): Promise<boolean> {
    const result = await db.delete(payments).where(eq(payments.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getDashboardStats(): Promise<{
    todaysSales: string;
    inventoryValue: string;
    inventoryCount: number;
    pendingPayments: string;
    overdueCount: number;
    lowStockCount: number;
  }> {
    const today = new Date();

    // Get today's sales
    const todaysSalesResult = await db
      .select({ total: sql`COALESCE(SUM(${invoices.totalAmount}), 0)` })
      .from(invoices)
      .where(eq(invoices.invoiceDate, today));
    
    // Get inventory count and value (simplified calculation)
    const inventoryResult = await db
      .select({ 
        count: sql`COUNT(*)`,
        value: sql`COALESCE(SUM(${inventoryItems.makingCharges} + ${inventoryItems.stoneCharges}), 0)`
      })
      .from(inventoryItems)
      .where(eq(inventoryItems.isActive, true));

    // Get pending payments
    const pendingResult = await db
      .select({ total: sql`COALESCE(SUM(${invoices.balanceAmount}), 0)` })
      .from(invoices)
      .where(sql`${invoices.balanceAmount} > 0`);

    // Get overdue count (invoices past due date)
    const overdueResult = await db
      .select({ count: sql`COUNT(*)` })
      .from(invoices)
      .where(and(
        sql`${invoices.balanceAmount} > 0`,
        sql`${invoices.dueDate} < CURRENT_DATE`
      ));

    // Get low stock count (items with quantity <= 5)
    const lowStockResult = await db
      .select({ count: sql`COUNT(*)` })
      .from(inventoryItems)
      .where(and(
        eq(inventoryItems.isActive, true),
        sql`${inventoryItems.quantity} <= 5`
      ));

    return {
      todaysSales: todaysSalesResult[0]?.total?.toString() || "0",
      inventoryValue: inventoryResult[0]?.value?.toString() || "0",
      inventoryCount: Number(inventoryResult[0]?.count) || 0,
      pendingPayments: pendingResult[0]?.total?.toString() || "0",
      overdueCount: Number(overdueResult[0]?.count) || 0,
      lowStockCount: Number(lowStockResult[0]?.count) || 0,
    };
  }

  // Settings methods
  async getSettings(category?: string): Promise<Setting[]> {
    if (category) {
      return await db.select().from(settings).where(eq(settings.category, category));
    }
    return await db.select().from(settings);
  }

  async getSetting(category: string, key: string): Promise<Setting | undefined> {
    const [setting] = await db.select().from(settings)
      .where(and(eq(settings.category, category), eq(settings.key, key)));
    return setting || undefined;
  }

  async setSetting(setting: InsertSetting): Promise<Setting> {
    // Check if setting exists
    const existing = await this.getSetting(setting.category, setting.key);

    // Ensure value is properly stored as JSON (not double-encoded)
    let processedValue = setting.value;

    // If the value is already a string that looks like JSON, don't double-encode it
    if (typeof processedValue === 'string') {
      try {
        // Try to parse it - if it parses, it's already JSON, so use the parsed value
        processedValue = JSON.parse(processedValue);
      } catch {
        // If it doesn't parse, it's a regular string, keep it as is
      }
    }

    if (existing) {
      // Update existing setting
      await db.update(settings)
        .set({ value: processedValue, description: setting.description, updatedAt: new Date() })
        .where(and(eq(settings.category, setting.category), eq(settings.key, setting.key)));

      const [updated] = await db.select().from(settings)
        .where(and(eq(settings.category, setting.category), eq(settings.key, setting.key)));
      return updated;
    } else {
      // Create new setting
      const settingToInsert = { ...setting, value: processedValue };
      const result = await db.insert(settings).values(settingToInsert);
      const insertId = Array.isArray(result) ? result[0].insertId : result.insertId;
      const [newSetting] = await db.select().from(settings).where(eq(settings.id, insertId));
      return newSetting;
    }
  }

  async deleteSetting(category: string, key: string): Promise<boolean> {
    try {
      await db.delete(settings).where(and(eq(settings.category, category), eq(settings.key, key)));
      return true;
    } catch (error) {
      console.error('Error deleting setting:', error);
      return false;
    }
  }

  // Method to clean up malformed JSON values in settings
  async cleanupSettingsValues(): Promise<void> {
    try {
      const allSettings = await this.getSettings();
      console.log(`Found ${allSettings.length} settings to check`);

      for (const setting of allSettings) {
        let needsUpdate = false;
        let cleanValue = setting.value;
        const originalValue = setting.value;

        console.log(`Checking setting ${setting.category}.${setting.key}:`, typeof setting.value, setting.value);

        // Check if value is a string that contains JSON
        if (typeof setting.value === 'string') {
          try {
            const parsed = JSON.parse(setting.value);
            console.log(`  Parsed once:`, typeof parsed, parsed);

            if (typeof parsed === 'string') {
              // This might be double-encoded, try parsing again
              try {
                const doubleParsed = JSON.parse(parsed);
                console.log(`  Double-parsed:`, typeof doubleParsed, doubleParsed);
                cleanValue = doubleParsed;
                needsUpdate = true;
              } catch {
                // Single-encoded string, use the first parse result
                cleanValue = parsed;
                needsUpdate = true;
              }
            } else {
              // Already properly parsed JSON (number, boolean, object, array)
              cleanValue = parsed;
              needsUpdate = true;
            }
          } catch {
            // Not JSON, leave as is (but this shouldn't happen with our schema)
            console.log(`  Not JSON, leaving as is`);
          }
        }

        if (needsUpdate) {
          console.log(`  Updating ${setting.category}.${setting.key} from:`, originalValue, 'to:', cleanValue);
          await db.update(settings)
            .set({ value: cleanValue, updatedAt: new Date() })
            .where(eq(settings.id, setting.id));
        }
      }

      console.log('Settings cleanup completed');
    } catch (error) {
      console.error('Error cleaning up settings:', error);
    }
  }

  // Method to seed default settings
  async seedDefaultSettings(): Promise<void> {
    try {
      const defaultSettings = [
        // General settings
        { category: "general", key: "app_name", value: "JewelPro Billing", description: "Application name" },
        { category: "general", key: "description", value: "Comprehensive jewelry wholesale billing and inventory management system", description: "Application description" },
        { category: "general", key: "auto_save", value: true, description: "Enable auto-save functionality" },
        { category: "general", key: "analytics", value: true, description: "Enable usage analytics" },

        // Appearance settings
        { category: "appearance", key: "theme", value: "light", description: "Application theme" },
        { category: "appearance", key: "primaryColor", value: "emerald", description: "Primary color scheme" },
        { category: "appearance", key: "fontSize", value: "medium", description: "Font size preference" },
        { category: "appearance", key: "compactMode", value: false, description: "Enable compact mode" },
        { category: "appearance", key: "animations", value: true, description: "Enable animations" },

        // Notification settings
        { category: "notifications", key: "newOrders", value: true, description: "Notify on new orders" },
        { category: "notifications", key: "lowStockAlerts", value: true, description: "Alert on low stock" },
        { category: "notifications", key: "paymentReceived", value: true, description: "Notify on payment received" },
        { category: "notifications", key: "rateChanges", value: false, description: "Alert on rate changes" },
        { category: "notifications", key: "systemUpdates", value: true, description: "Notify on system updates" },
        { category: "notifications", key: "emailNotifications", value: "", description: "Email for notifications" },
        { category: "notifications", key: "smsNotifications", value: "", description: "Phone for SMS alerts" },

        // Security settings
        { category: "security", key: "twoFactor", value: false, description: "Enable two-factor authentication" },
        { category: "security", key: "sessionTimeout", value: true, description: "Enable session timeout" },
        { category: "security", key: "sessionDuration", value: "60", description: "Session duration in minutes" },

        // Database settings
        { category: "database", key: "autoBackup", value: true, description: "Enable automatic backups" },
        { category: "database", key: "backupFrequency", value: "daily", description: "Backup frequency" },
        { category: "database", key: "retentionPeriod", value: "30", description: "Backup retention period in days" },

        // Localization settings
        { category: "localization", key: "language", value: "en", description: "Application language" },
        { category: "localization", key: "currency", value: "inr", description: "Default currency" },
        { category: "localization", key: "timezone", value: "asia_kolkata", description: "Timezone" },
        { category: "localization", key: "dateFormat", value: "dd_mm_yyyy", description: "Date format preference" },
        { category: "localization", key: "numberFormat", value: "indian", description: "Number format preference" },
      ];

      for (const setting of defaultSettings) {
        const existing = await this.getSetting(setting.category, setting.key);
        if (!existing) {
          await this.setSetting(setting);
          console.log(`Seeded default setting: ${setting.category}.${setting.key}`);
        }
      }

      console.log('Default settings seeding completed');
    } catch (error) {
      console.error('Error seeding default settings:', error);
    }
  }

  // Template methods
  async getTemplates(): Promise<Template[]> {
    return await db.select().from(templates).where(eq(templates.isActive, true));
  }

  async getTemplate(id: string): Promise<Template | undefined> {
    const [template] = await db.select().from(templates)
      .where(and(eq(templates.id, id), eq(templates.isActive, true)));
    return template || undefined;
  }

  async createTemplate(template: InsertTemplate): Promise<Template> {
    const result = await db.insert(templates).values(template);
    const [newTemplate] = await db.select().from(templates).where(eq(templates.id, template.id));
    return newTemplate;
  }

  async updateTemplate(id: string, template: Partial<InsertTemplate>): Promise<Template | undefined> {
    await db.update(templates)
      .set({ ...template, updatedAt: new Date() })
      .where(eq(templates.id, id));

    const [updated] = await db.select().from(templates).where(eq(templates.id, id));
    return updated || undefined;
  }

  async deleteTemplate(id: string): Promise<boolean> {
    try {
      // Soft delete by setting isActive to false
      await db.update(templates)
        .set({ isActive: false, updatedAt: new Date() })
        .where(eq(templates.id, id));
      return true;
    } catch (error) {
      console.error('Error deleting template:', error);
      return false;
    }
  }

  async getTemplateStats(): Promise<{ total: number; active: number; custom: number; system: number }> {
    const allTemplates = await db.select().from(templates);

    return {
      total: allTemplates.length,
      active: allTemplates.filter(t => t.isActive).length,
      custom: allTemplates.filter(t => t.createdBy !== 'system').length,
      system: allTemplates.filter(t => t.createdBy === 'system').length
    };
  }

  // Method to create templates table if it doesn't exist
  async createTemplatesTable(): Promise<void> {
    try {
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS templates (
          id VARCHAR(50) PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          description VARCHAR(500),
          category VARCHAR(20) NOT NULL DEFAULT 'custom',
          colors JSON NOT NULL,
          layout JSON NOT NULL,
          settings JSON NOT NULL,
          custom_fields JSON,
          is_default BOOLEAN NOT NULL DEFAULT FALSE,
          is_active BOOLEAN NOT NULL DEFAULT TRUE,
          created_by VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      console.log('Templates table created or already exists');
    } catch (error) {
      console.error('Error creating templates table:', error);
    }
  }

  // Method to seed default templates
  async seedDefaultTemplates(): Promise<void> {
    try {
      // First ensure the table exists
      await this.createTemplatesTable();

      // Check if templates already exist
      let existingTemplates;
      try {
        existingTemplates = await this.getTemplates();
      } catch (error) {
        // If table doesn't exist, getTemplates will fail, so we continue with seeding
        existingTemplates = [];
      }

      if (existingTemplates.length > 0) {
        console.log('Templates already exist, skipping seeding');
        return;
      }

      const defaultTemplates = [
        {
          id: 'modern-blue',
          name: 'Modern Blue',
          description: 'Clean and modern template with blue accent colors',
          category: 'modern' as const,
          colors: {
            primary: [41, 128, 185],
            accent: [39, 174, 96],
            dark: [44, 62, 80],
            lightGray: [236, 240, 241],
            mediumGray: [149, 165, 166]
          },
          layout: {
            margin: 6,
            headerHeight: 28,
            sectionSpacing: 8,
            fontSize: {
              header: 18,
              subheader: 12,
              body: 8,
              small: 6
            }
          },
          settings: {
            showLogo: true,
            showWatermark: false,
            showGSTBreakdown: false,
            showBankDetails: true,
            showTermsAndConditions: true,
            compactMode: true,
            currency: '₹',
            dateFormat: 'DD/MM/YYYY',
            numberFormat: 'en-IN'
          },
          customFields: {},
          isDefault: true,
          isActive: true,
          createdBy: 'system'
        },
        {
          id: 'luxury-gold',
          name: 'Luxury Gold',
          description: 'Premium template with gold accents for high-end jewelry',
          category: 'luxury' as const,
          colors: {
            primary: [212, 175, 55],
            accent: [184, 134, 11],
            dark: [92, 57, 0],
            lightGray: [254, 252, 232],
            mediumGray: [161, 138, 78]
          },
          layout: {
            margin: 8,
            headerHeight: 35,
            sectionSpacing: 10,
            fontSize: {
              header: 20,
              subheader: 14,
              body: 9,
              small: 7
            }
          },
          settings: {
            showLogo: true,
            showWatermark: true,
            showGSTBreakdown: false,
            showBankDetails: true,
            showTermsAndConditions: true,
            compactMode: false,
            currency: '₹',
            dateFormat: 'DD/MM/YYYY',
            numberFormat: 'en-IN'
          },
          customFields: {},
          isDefault: false,
          isActive: true,
          createdBy: 'system'
        },
        {
          id: 'minimal-gray',
          name: 'Minimal Gray',
          description: 'Clean minimal design with gray tones',
          category: 'minimal' as const,
          colors: {
            primary: [75, 85, 99],
            accent: [107, 114, 128],
            dark: [31, 41, 55],
            lightGray: [249, 250, 251],
            mediumGray: [156, 163, 175]
          },
          layout: {
            margin: 5,
            headerHeight: 25,
            sectionSpacing: 6,
            fontSize: {
              header: 16,
              subheader: 10,
              body: 7,
              small: 5
            }
          },
          settings: {
            showLogo: false,
            showWatermark: false,
            showGSTBreakdown: false,
            showBankDetails: true,
            showTermsAndConditions: false,
            compactMode: true,
            currency: '₹',
            dateFormat: 'DD/MM/YYYY',
            numberFormat: 'en-IN'
          },
          customFields: {},
          isDefault: false,
          isActive: true,
          createdBy: 'system'
        },
        {
          id: 'classic-green',
          name: 'Classic Green',
          description: 'Traditional template with green color scheme',
          category: 'classic' as const,
          colors: {
            primary: [34, 139, 34],
            accent: [50, 205, 50],
            dark: [0, 100, 0],
            lightGray: [240, 255, 240],
            mediumGray: [144, 238, 144]
          },
          layout: {
            margin: 10,
            headerHeight: 40,
            sectionSpacing: 12,
            fontSize: {
              header: 22,
              subheader: 16,
              body: 10,
              small: 8
            }
          },
          settings: {
            showLogo: true,
            showWatermark: false,
            showGSTBreakdown: true,
            showBankDetails: true,
            showTermsAndConditions: true,
            compactMode: false,
            currency: '₹',
            dateFormat: 'DD/MM/YYYY',
            numberFormat: 'en-IN'
          },
          customFields: {},
          isDefault: false,
          isActive: true,
          createdBy: 'system'
        }
      ];

      for (const template of defaultTemplates) {
        await this.createTemplate(template);
        console.log(`Seeded default template: ${template.name}`);
      }

      console.log('Default templates seeding completed');
    } catch (error) {
      console.error('Error seeding default templates:', error);
    }
  }
}

export const storage = new DatabaseStorage();
