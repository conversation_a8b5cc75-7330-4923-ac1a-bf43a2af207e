import jsPDF from 'jspdf';

interface InvoiceData {
  invoice: {
    id: number;
    invoiceNumber: string;
    invoiceDate: string;
    dueDate: string;
    subtotal: string;
    cgstAmount: string;
    sgstAmount: string;
    igstAmount: string;
    discountAmount: string;
    totalAmount: string;
    paidAmount: string;
    balanceAmount: string;
    notes?: string;
    status: string;
    ewayBillNumber?: string;
    paymentTerms?: string;
  };
  customer: {
    id: number;
    name: string;
    email: string;
    phone: string;
    address: string;
    gstin?: string;
    pan?: string;
    stateCode?: string;
  };
  items: Array<{
    id: number;
    itemId: number;
    itemName: string;
    quantity: number;
    grossWeight: string;
    netWeight: string;
    fineWeight?: string;
    stoneWeight?: string;
    stoneAmount?: string;
    purityPercentage?: string;
    wastagePercentage?: string;
    ratePerGram: string;
    goldValue?: string;
    makingCharges?: string;
    stoneCharges?: string;
    itemTotal: string;
    taxableAmount?: string;
    cgstAmount?: string;
    sgstAmount?: string;
    totalAmount: string;
    metalType?: string;
    purity?: string;
    hsnCode?: string;
  }>;
  company?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    gstin: string;
    pan?: string;
    stateCode?: string;
    website?: string;
    bankDetails?: {
      bankName: string;
      accountNumber: string;
      ifscCode: string;
      branch: string;
    };
  };
  metalRates?: {
    gold: number;
    silver: number;
    date: string;
  };
}

/**
 * Utility functions for text processing and number-to-words conversion
 */
function splitText(text: string | undefined, maxWidth: number): string[] {
  if (!text) return ['N/A'];
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';
  words.forEach(word => {
    if ((currentLine + word).length <= maxWidth) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  });
  if (currentLine) lines.push(currentLine);
  return lines;
}

function truncateText(text: string | undefined, maxLength: number): string {
  if (!text) return 'N/A';
  return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
}

function convertToWords(amount: number): string {
  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  if (amount === 0) return 'Zero Rupees Only';
  const rupees = Math.floor(amount);
  const paise = Math.round((amount - rupees) * 100);
  let result = convertNumberToWords(rupees, ones, teens, tens);
  if (result) result += ' Rupees';
  if (paise > 0) {
    result += (result ? ' and ' : '') + convertNumberToWords(paise, ones, teens, tens) + ' Paise';
  }
  return result + ' Only';
}

function convertNumberToWords(num: number, ones: string[], teens: string[], tens: string[]): string {
  if (num === 0) return '';
  if (num < 10) return ones[num];
  if (num < 20) return teens[num - 10];
  if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 ? ' ' + ones[num % 10] : '');
  if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 ? ' ' + convertNumberToWords(num % 100, ones, teens, tens) : '');
  if (num < 100000) return convertNumberToWords(Math.floor(num / 1000), ones, teens, tens) + ' Thousand' + (num % 1000 ? ' ' + convertNumberToWords(num % 1000, ones, teens, tens) : '');
  if (num < 10000000) return convertNumberToWords(Math.floor(num / 100000), ones, teens, tens) + ' Lakh' + (num % 100000 ? ' ' + convertNumberToWords(num % 100000, ones, teens, tens) : '');
  return convertNumberToWords(Math.floor(num / 10000000), ones, teens, tens) + ' Crore' + (num % 10000000 ? ' ' + convertNumberToWords(num % 10000000, ones, teens, tens) : '');
}

/**
 * Enhanced ProfessionalBillGenerator for robust PDF invoice generation.
 * Supports page breaks, configuration, and improved error handling.
 */
class ProfessionalBillGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number;
  private currentY: number;
  private config: Partial<{
    primaryColor: [number, number, number];
    accentColor: [number, number, number];
    darkColor: [number, number, number];
    lightGray: [number, number, number];
    mediumGray: [number, number, number];
    font: string;
    currency: string;
    taxLabels: { cgst: string; sgst: string; };
  }>;
  private primaryColor: [number, number, number];
  private accentColor: [number, number, number];
  private darkColor: [number, number, number];
  private lightGray: [number, number, number];
  private mediumGray: [number, number, number];
  private font: string;
  private currency: string;
  private taxLabels: { cgst: string; sgst: string; };

  /**
   * @param config Optional configuration for colors, fonts, and currency
   */
  constructor(config: ProfessionalBillGenerator['config'] = {}) {
    this.doc = new jsPDF('p', 'mm', 'a4');
    this.pageWidth = this.doc.internal.pageSize.width;
    this.pageHeight = this.doc.internal.pageSize.height;
    this.margin = 15;
    this.currentY = 20;
    this.config = config;
    this.primaryColor = config.primaryColor ?? [41, 128, 185];
    this.accentColor = config.accentColor ?? [39, 174, 96];
    this.darkColor = config.darkColor ?? [44, 62, 80];
    this.lightGray = config.lightGray ?? [236, 240, 241];
    this.mediumGray = config.mediumGray ?? [149, 165, 166];
    this.font = config.font ?? 'helvetica';
    this.currency = config.currency ?? '₹';
    this.taxLabels = config.taxLabels ?? { cgst: 'CGST (1.5%)', sgst: 'SGST (1.5%)' };
  }

  private setFillColor(color: [number, number, number]): void {
    this.doc.setFillColor(color[0], color[1], color[2]);
  }
  private setTextColor(color: [number, number, number]): void {
    this.doc.setTextColor(color[0], color[1], color[2]);
  }
  private setDrawColor(color: [number, number, number]): void {
    this.doc.setDrawColor(color[0], color[1], color[2]);
  }

  /**
   * Generates the professional bill PDF from invoice data.
   * @param data InvoiceData object
   */
  generateProfessionalBill(data: InvoiceData): void {
    try {
      if (!data?.invoice || !data?.customer || !Array.isArray(data?.items)) {
        throw new Error('Invalid invoice data structure');
      }
      this.addProfessionalHeader(data);
      this.addBusinessInformation(data);
      this.addInvoiceDetails(data);
      this.addItemizedTable(data);
      this.addCalculationsSection(data);
      this.addGSTBreakdown(data);
      this.addFooterSection(data);
    } catch (error: any) {
      // Add error message to PDF
      this.doc.setTextColor(255, 0, 0);
      this.doc.setFontSize(16);
      this.doc.setFont(this.font, 'bold');
      this.doc.text('Error generating invoice', this.pageWidth/2, this.pageHeight/2, { align: 'center' });
      this.doc.setFontSize(12);
      this.doc.setFont(this.font, 'normal');
      this.doc.text('Please contact support for assistance', this.pageWidth/2, this.pageHeight/2 + 10, { align: 'center' });
      throw error;
    }
  }

  private addProfessionalHeader(data: InvoiceData): void {
    // Header background
    this.setFillColor(this.primaryColor);
    this.doc.rect(0, 0, this.pageWidth, 40, 'F');
    
    // Company name
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(22);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'JEWELRY HOUSE', this.margin, 18);
    
    // Tagline
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text('Premium Jewelry & Ornaments', this.margin, 25);
    
    // TAX INVOICE
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TAX INVOICE', this.pageWidth - this.margin, 18, { align: 'right' });
    
    // Invoice number
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`Invoice #${data.invoice.invoiceNumber}`, this.pageWidth - this.margin, 28, { align: 'right' });
    
    // Status badge
    const statusColor = data.invoice.status === 'paid' ? this.accentColor : [231, 76, 60] as [number, number, number];
    this.doc.setFillColor(statusColor[0], statusColor[1], statusColor[2]);
    this.doc.roundedRect(this.pageWidth - this.margin - 25, 30, 25, 6, 2, 2, 'F');
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.invoice.status.toUpperCase(), this.pageWidth - this.margin - 12.5, 34, { align: 'center' });
    
    this.currentY = 50;
  }

  private addBusinessInformation(data: InvoiceData): void {
    // Business information section
    this.setFillColor(this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 45, 'F');
    
    // Border
    this.setDrawColor(this.mediumGray);
    this.doc.setLineWidth(0.5);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 45, 'D');
    
    // From section
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('FROM:', this.margin + 5, this.currentY + 10);
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'Company Name', this.margin + 5, this.currentY + 18);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(9);
    
    // Company address
    const companyAddress = splitText(data.company?.address, 50);
    let yPos = this.currentY + 24;
    companyAddress.forEach(line => {
      this.doc.text(line, this.margin + 5, yPos);
      yPos += 4;
    });
    
    // Company contact
    this.doc.text(`Phone: ${data.company?.phone || 'N/A'}`, this.margin + 5, yPos);
    this.doc.text(`Email: ${data.company?.email || 'N/A'}`, this.margin + 5, yPos + 4);
    this.doc.text(`GSTIN: ${data.company?.gstin || 'N/A'}`, this.margin + 5, yPos + 8);
    
    // Bill To section
    const rightX = this.pageWidth / 2 + 10;
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('BILL TO:', rightX, this.currentY + 10);
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.customer.name, rightX, this.currentY + 18);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(9);
    
    // Customer address
    const customerAddress = splitText(data.customer.address, 50);
    yPos = this.currentY + 24;
    customerAddress.forEach(line => {
      this.doc.text(line, rightX, yPos);
      yPos += 4;
    });
    
    // Customer contact
    this.doc.text(`Phone: ${data.customer.phone}`, rightX, yPos);
    this.doc.text(`Email: ${data.customer.email}`, rightX, yPos + 4);
    if (data.customer.gstin) {
      this.doc.text(`GSTIN: ${data.customer.gstin}`, rightX, yPos + 8);
    }
    
    this.currentY += 55;
  }

  private addInvoiceDetails(data: InvoiceData): void {
    // Invoice details banner
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 20, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    
    // Left side details
    this.doc.text(`Invoice Date: ${data.invoice.invoiceDate}`, this.margin + 5, this.currentY + 8);
    this.doc.text(`Due Date: ${data.invoice.dueDate}`, this.margin + 5, this.currentY + 15);
    
    // Center - Metal rates
    if (data.metalRates) {
      const centerX = this.pageWidth / 2;
      this.doc.text(`Gold: ₹${data.metalRates.gold}/g`, centerX - 20, this.currentY + 8);
      this.doc.text(`Silver: ₹${data.metalRates.silver}/g`, centerX - 20, this.currentY + 15);
    }
    
    // Right side
    this.doc.text(`Payment Terms: ${data.invoice.paymentTerms || '30 Days'}`, this.pageWidth - this.margin - 5, this.currentY + 8, { align: 'right' });
    if (data.invoice.ewayBillNumber) {
      this.doc.text(`E-Way Bill: ${data.invoice.ewayBillNumber}`, this.pageWidth - this.margin - 5, this.currentY + 15, { align: 'right' });
    }
    
    this.currentY += 30;
  }

  private addItemizedTable(data: InvoiceData): void {
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('ITEMIZED JEWELRY DETAILS', this.margin, this.currentY);
    this.currentY += 10;
    const rowHeight = 15;
    const headerHeight = 12;
    const columns = [
      { header: '#', width: 12, align: 'center' },
      { header: 'Item Description', width: 40, align: 'left' },
      { header: 'HSN', width: 15, align: 'center' },
      { header: 'Qty', width: 12, align: 'center' },
      { header: 'Metal/Purity', width: 22, align: 'center' },
      { header: 'Weights (g)', width: 28, align: 'center' },
      { header: 'Rate/g', width: 18, align: 'right' },
      { header: 'Making', width: 18, align: 'right' },
      { header: 'Stone', width: 16, align: 'right' },
      { header: 'Total', width: 22, align: 'right' }
    ];
    // Table header
    this.doc.setFillColor(...this.darkColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, headerHeight, 'F');
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'bold');
    let xPos = this.margin + 2;
    columns.forEach(col => {
      const textX = col.align === 'center' ? xPos + col.width / 2 : 
                   col.align === 'right' ? xPos + col.width - 2 : xPos + 2;
      this.doc.text(col.header, textX, this.currentY + 8, { align: col.align as any });
      xPos += col.width;
    });
    this.currentY += headerHeight;
    // Table rows
    if (!data.items || data.items.length === 0) {
      this.doc.setFillColor(250, 250, 250);
      this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'F');
      this.doc.setTextColor(...this.darkColor);
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'italic');
      this.doc.text('No items found', this.pageWidth/2, this.currentY + 8, { align: 'center' });
      this.currentY += rowHeight;
    } else {
      data.items.forEach((item, index) => {
        // Page break if needed
        if (this.currentY + rowHeight > this.pageHeight - this.margin - 40) {
          this.doc.addPage();
          this.currentY = this.margin;
          // Redraw table header on new page
          this.doc.setFillColor(...this.darkColor);
          this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, headerHeight, 'F');
          this.doc.setTextColor(255, 255, 255);
          this.doc.setFontSize(9);
          this.doc.setFont('helvetica', 'bold');
          let xPos2 = this.margin + 2;
          columns.forEach(col => {
            const textX = col.align === 'center' ? xPos2 + col.width / 2 : 
                         col.align === 'right' ? xPos2 + col.width - 2 : xPos2 + 2;
            this.doc.text(col.header, textX, this.currentY + 8, { align: col.align as any });
            xPos2 += col.width;
          });
          this.currentY += headerHeight;
        }
        // Alternating row colors
        if (index % 2 === 0) {
          this.doc.setFillColor(250, 250, 250);
          this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'F');
        }
        this.doc.setDrawColor(...this.mediumGray);
        this.doc.setLineWidth(0.2);
        this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'D');
        this.doc.setTextColor(...this.darkColor);
        this.doc.setFontSize(8);
        this.doc.setFont('helvetica', 'normal');
        xPos = this.margin + 2;
        // Serial number
        this.doc.text((index + 1).toString(), xPos + 6, this.currentY + 6, { align: 'center' });
        xPos += 12;
        // Item description (multi-line)
        this.doc.setFont('helvetica', 'bold');
        this.doc.text(truncateText(item.itemName || 'Jewelry Item', 35), xPos + 2, this.currentY + 5);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setFontSize(7);
        this.doc.text(`Code: ${item.itemId ?? 'N/A'}`, xPos + 2, this.currentY + 9);
        this.doc.text(`Desc: Premium Quality`, xPos + 2, this.currentY + 12);
        this.doc.setFontSize(8);
        xPos += 40;
        // HSN Code
        this.doc.text(item.hsnCode ?? '7113', xPos + 7.5, this.currentY + 8, { align: 'center' });
        xPos += 15;
        // Quantity
        this.doc.text((item.quantity ?? 1).toString(), xPos + 6, this.currentY + 8, { align: 'center' });
        xPos += 12;
        // Metal/Purity
        this.doc.text(`${item.metalType ?? 'Gold'}`, xPos + 11, this.currentY + 5, { align: 'center' });
        this.doc.text(`${item.purity ?? '22K'}`, xPos + 11, this.currentY + 10, { align: 'center' });
        xPos += 22;
        // Weights
        this.doc.setFontSize(7);
        this.doc.text(`G: ${item.grossWeight ?? '0'}`, xPos + 2, this.currentY + 4);
        this.doc.text(`N: ${item.netWeight ?? '0'}`, xPos + 15, this.currentY + 4);
        this.doc.text(`S: ${item.stoneWeight ?? '0'}`, xPos + 2, this.currentY + 8);
        this.doc.text(`F: ${item.fineWeight ?? item.netWeight ?? '0'}`, xPos + 15, this.currentY + 8);
        this.doc.text(`Purity: ${item.purityPercentage ?? '91.6'}%`, xPos + 2, this.currentY + 12);
        this.doc.setFontSize(8);
        xPos += 28;
        // Rate per gram
        const ratePerGram = parseFloat(item.ratePerGram ?? '0');
        this.doc.text(`${this.currency}${ratePerGram.toLocaleString('en-IN')}`, xPos + 16, this.currentY + 8, { align: 'right' });
        xPos += 18;
        // Making charges
        const makingCharges = parseFloat(item.makingCharges ?? '0');
        this.doc.text(`${this.currency}${makingCharges.toLocaleString('en-IN')}`, xPos + 16, this.currentY + 8, { align: 'right' });
        xPos += 18;
        // Stone charges
        const stoneCharges = parseFloat(item.stoneCharges ?? '0');
        this.doc.text(`${this.currency}${stoneCharges.toLocaleString('en-IN')}`, xPos + 14, this.currentY + 8, { align: 'right' });
        xPos += 16;
        // Total amount
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(...this.accentColor);
        const totalAmount = parseFloat(item.totalAmount ?? '0');
        this.doc.text(`${this.currency}${totalAmount.toLocaleString('en-IN')}`, xPos + 20, this.currentY + 8, { align: 'right' });
        this.currentY += rowHeight;
      });
    }
    this.currentY += 10;
  }

  private addCalculationsSection(data: InvoiceData): void {
    // Summary section
    const summaryWidth = 70;
    const summaryX = this.pageWidth - this.margin - summaryWidth;
    
    // Summary background
    this.doc.setFillColor(...this.lightGray);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 35, 'F');
    
    // Summary border
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(1);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 35, 'D');
    
    // Summary header
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 8, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('INVOICE SUMMARY', summaryX + summaryWidth/2, this.currentY + 5.5, { align: 'center' });
    
    // Summary items
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'normal');
    
    let summaryY = this.currentY + 15;
    
    // Subtotal
    const subtotal = parseFloat(data.invoice.subtotal || '0');
    this.doc.text('Subtotal:', summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${subtotal.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    summaryY += 5;
    
    // CGST
    const cgstAmount = parseFloat(data.invoice.cgstAmount || '0');
    this.doc.text(`${this.taxLabels.cgst}:`, summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${cgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    summaryY += 5;
    
    // SGST
    const sgstAmount = parseFloat(data.invoice.sgstAmount || '0');
    this.doc.text(`${this.taxLabels.sgst}:`, summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${sgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    summaryY += 5;
    
    // Total line
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.line(summaryX + 3, summaryY, summaryX + summaryWidth - 3, summaryY);
    summaryY += 3;
    
    // Total amount
    const totalAmount = parseFloat(data.invoice.totalAmount || '0');
    this.doc.setFont('helvetica', 'bold');
    this.doc.setFontSize(11);
    this.doc.setTextColor(...this.accentColor);
    this.doc.text('TOTAL:', summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${totalAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    
    this.currentY += 45;
  }

  private addGSTBreakdown(data: InvoiceData): void {
    // GST section title
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('GST BREAKDOWN', this.margin, this.currentY);
    
    this.currentY += 8;
    
    // GST table
    const gstTableHeight = 20;
    
    // Header
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 8, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'bold');
    
    this.doc.text('HSN/SAC', this.margin + 5, this.currentY + 5.5);
    this.doc.text('Description', this.margin + 25, this.currentY + 5.5);
    this.doc.text('Taxable Value', this.margin + 70, this.currentY + 5.5);
    this.doc.text('CGST (1.5%)', this.margin + 105, this.currentY + 5.5);
    this.doc.text('SGST (1.5%)', this.margin + 135, this.currentY + 5.5);
    this.doc.text('Total Tax', this.margin + 165, this.currentY + 5.5);
    
    this.currentY += 8;
    
    // GST data row
    this.doc.setFillColor(250, 250, 250);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 12, 'F');
    
    this.doc.setDrawColor(...this.mediumGray);
    this.doc.setLineWidth(0.5);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 12, 'D');
    
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'normal');
    
    this.doc.text('7113', this.margin + 5, this.currentY + 7);
    this.doc.text('Jewelry Items', this.margin + 25, this.currentY + 7);
    
    const gstSubtotal = parseFloat(data.invoice.subtotal || '0');
    const gstCgstAmount = parseFloat(data.invoice.cgstAmount || '0');
    const gstSgstAmount = parseFloat(data.invoice.sgstAmount || '0');
    
    this.doc.text(`${this.currency}${gstSubtotal.toLocaleString('en-IN')}`, this.margin + 70, this.currentY + 7);
    this.doc.text(`${this.currency}${gstCgstAmount.toLocaleString('en-IN')}`, this.margin + 105, this.currentY + 7);
    this.doc.text(`${this.currency}${gstSgstAmount.toLocaleString('en-IN')}`, this.margin + 135, this.currentY + 7);
    
    const totalTax = gstCgstAmount + gstSgstAmount;
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(...this.accentColor);
    this.doc.text(`${this.currency}${totalTax.toLocaleString('en-IN')}`, this.margin + 165, this.currentY + 7);
    
    this.currentY += 20;
  }

  private addFooterSection(data: InvoiceData): void {
    // Amount in words
    this.doc.setFillColor(...this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 12, 'F');
    
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    
    const wordsAmount = parseFloat(data.invoice.totalAmount || '0');
    const amountInWords = convertToWords(wordsAmount);
    this.doc.text('Amount in Words:', this.margin + 5, this.currentY + 5);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(amountInWords, this.margin + 5, this.currentY + 9);
    
    this.currentY += 20;
    
    // Footer information in two columns
    const leftColWidth = (this.pageWidth - 3 * this.margin) / 2;
    
    // Bank details
    if (data.company?.bankDetails) {
      this.doc.setFillColor(255, 255, 255);
      this.doc.setDrawColor(...this.mediumGray);
      this.doc.setLineWidth(0.5);
      this.doc.rect(this.margin, this.currentY, leftColWidth, 30, 'FD');
      
      this.doc.setTextColor(...this.primaryColor);
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text('BANK DETAILS', this.margin + 3, this.currentY + 8);
      
      this.doc.setTextColor(...this.darkColor);
      this.doc.setFontSize(8);
      this.doc.setFont('helvetica', 'normal');
      
      let bankY = this.currentY + 14;
      this.doc.text(`Bank: ${data.company.bankDetails.bankName}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`Account: ${data.company.bankDetails.accountNumber}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`IFSC: ${data.company.bankDetails.ifscCode}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`Branch: ${data.company.bankDetails.branch}`, this.margin + 3, bankY);
    }
    
    // Terms and signature
    const rightColX = this.margin + leftColWidth + this.margin;
    this.doc.setFillColor(255, 255, 255);
    this.doc.setDrawColor(...this.mediumGray);
    this.doc.setLineWidth(0.5);
    this.doc.rect(rightColX, this.currentY, leftColWidth, 30, 'FD');
    
    this.doc.setTextColor(...this.primaryColor);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TERMS & CONDITIONS', rightColX + 3, this.currentY + 8);
    
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    
    let termsY = this.currentY + 14;
    this.doc.text('• Payment due within 30 days', rightColX + 3, termsY);
    termsY += 4;
    this.doc.text('• Goods once sold will not be taken back', rightColX + 3, termsY);
    termsY += 4;
    this.doc.text('• Subject to local jurisdiction', rightColX + 3, termsY);
    
    // Signature
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Authorized Signatory', rightColX + leftColWidth - 3, this.currentY + 26, { align: 'right' });
    
    // Footer line
    this.currentY = this.pageHeight - 20;
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(2);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    
    // Thank you message
    this.doc.setTextColor(...this.primaryColor);
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Thank you for your business!', this.pageWidth/2, this.currentY + 8, { align: 'center' });
    
    // Contact info
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`${data.company?.phone || ''} | ${data.company?.email || ''} | ${data.company?.website || ''}`, 
                  this.pageWidth/2, this.currentY + 12, { align: 'center' });
  }

  save(filename: string = 'professional-jewelry-bill.pdf'): void {
    this.doc.save(filename);
  }

  print(): void {
    if (typeof window !== 'undefined') {
      window.open(this.doc.output('bloburl'), '_blank');
    } else {
      console.log('PDF preview is not available in this environment.');
    }
  }
}

// Export functions
export function generateProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config']): void {
  const generator = new ProfessionalBillGenerator(config);
  generator.generateProfessionalBill(data);
  generator.save(`Professional-Bill-${data.invoice.invoiceNumber}.pdf`);
}

export function printProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config']): void {
  const generator = new ProfessionalBillGenerator(config);
  generator.generateProfessionalBill(data);
  generator.print();
}