import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { apiRequest } from './api';
import { Template } from '../hooks/useTemplates';

// Define paper sizes and orientations
type PaperSize = 'a4' | 'letter' | 'legal';
type PaperOrientation = 'portrait' | 'landscape';

// Paper dimensions in mm
const PAPER_DIMENSIONS = {
  a4: { width: 210, height: 297 },
  letter: { width: 215.9, height: 279.4 },
  legal: { width: 215.9, height: 355.6 }
};

// Function to get paper dimensions
function getPaperDimensions(size: PaperSize, orientation: PaperOrientation) {
  const dimensions = PAPER_DIMENSIONS[size];
  return orientation === 'portrait'
    ? { width: dimensions.width, height: dimensions.height }
    : { width: dimensions.height, height: dimensions.width };
}

// Global cache for templates
let templateCache: Record<string, Template> = {};
let defaultTemplateCache: Template | null = null;

// Function to fetch template by ID
async function fetchTemplateById(id: string): Promise<Template | null> {
  if (templateCache[id]) {
    return templateCache[id];
  }

  try {
    const response = await apiRequest('GET', `/api/templates/${id}`);
    const template = await response.json();
    templateCache[id] = template;
    return template;
  } catch (error) {
    console.error('Error fetching template:', error);
    return null;
  }
}

// Function to fetch default template
async function fetchDefaultTemplate(): Promise<Template | null> {
  if (defaultTemplateCache) {
    return defaultTemplateCache;
  }

  try {
    const response = await apiRequest('GET', '/api/templates');
    const templates = await response.json();
    const defaultTemplate = templates.find((t: Template) => t.isDefault && t.isActive);

    if (defaultTemplate) {
      defaultTemplateCache = defaultTemplate;
      return defaultTemplate;
    }

    // Fallback to first active template
    const firstTemplate = templates.find((t: Template) => t.isActive);
    if (firstTemplate) {
      defaultTemplateCache = firstTemplate;
      return firstTemplate;
    }

    return null;
  } catch (error) {
    console.error('Error fetching default template:', error);
    return null;
  }
}

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface InvoiceData {
  invoice: {
    id: number;
    invoiceNumber: string;
    invoiceDate: string;
    dueDate: string;
    subtotal: string;
    cgstAmount: string;
    sgstAmount: string;
    igstAmount: string;
    discountAmount: string;
    totalAmount: string;
    paidAmount: string;
    balanceAmount: string;
    notes?: string;
    status: string;
    ewayBillNumber?: string;
    paymentTerms?: string;
  };
  customer: {
    id: number;
    name: string;
    email: string;
    phone: string;
    address: string;
    gstin?: string;
    pan?: string;
    stateCode?: string;
  };
  items: Array<{
    id: number;
    itemId: number;
    itemName: string;
    quantity: number;
    grossWeight: string;
    netWeight: string;
    fineWeight?: string;
    stoneWeight?: string;
    stoneAmount?: string;
    purityPercentage?: string;
    wastagePercentage?: string;
    ratePerGram: string;
    goldValue?: string;
    makingCharges?: string;
    stoneCharges?: string;
    itemTotal: string;
    taxableAmount?: string;
    cgstAmount?: string;
    sgstAmount?: string;
    totalAmount: string;
    metalType?: string;
    purity?: string;
    hsnCode?: string;
  }>;
  company?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    gstin: string;
    pan?: string;
    stateCode?: string;
    website?: string;
    bankDetails?: {
      bankName: string;
      accountNumber: string;
      ifscCode: string;
      branch: string;
    };
  };
  metalRates?: {
    gold: number;
    silver: number;
    date: string;
  };
}

/**
 * Utility functions for text processing and number-to-words conversion
 */
function splitText(text: string | undefined, maxWidth: number): string[] {
  if (!text) return ['N/A'];
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';
  words.forEach(word => {
    if ((currentLine + word).length <= maxWidth) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  });
  if (currentLine) lines.push(currentLine);
  return lines;
}

function truncateText(text: string | undefined, maxLength: number): string {
  if (!text) return 'N/A';
  return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
}

function convertToWords(amount: number): string {
  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  if (amount === 0) return 'Zero Rupees Only';
  const rupees = Math.floor(amount);
  const paise = Math.round((amount - rupees) * 100);
  let result = convertNumberToWords(rupees, ones, teens, tens);
  if (result) result += ' Rupees';
  if (paise > 0) {
    result += (result ? ' and ' : '') + convertNumberToWords(paise, ones, teens, tens) + ' Paise';
  }
  return result + ' Only';
}

function convertNumberToWords(num: number, ones: string[], teens: string[], tens: string[]): string {
  if (num === 0) return '';
  if (num < 10) return ones[num];
  if (num < 20) return teens[num - 10];
  if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 ? ' ' + ones[num % 10] : '');
  if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 ? ' ' + convertNumberToWords(num % 100, ones, teens, tens) : '');
  if (num < 100000) return convertNumberToWords(Math.floor(num / 1000), ones, teens, tens) + ' Thousand' + (num % 1000 ? ' ' + convertNumberToWords(num % 1000, ones, teens, tens) : '');
  if (num < 10000000) return convertNumberToWords(Math.floor(num / 100000), ones, teens, tens) + ' Lakh' + (num % 100000 ? ' ' + convertNumberToWords(num % 100000, ones, teens, tens) : '');
  return convertNumberToWords(Math.floor(num / 10000000), ones, teens, tens) + ' Crore' + (num % 10000000 ? ' ' + convertNumberToWords(num % 10000000, ones, teens, tens) : '');
}

/**
 * Enhanced ProfessionalBillGenerator for robust PDF invoice generation.
 * Supports page breaks, configuration, and improved error handling.
 */
class ProfessionalBillGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number;
  private currentY: number;
  private config: Partial<{
    primaryColor: [number, number, number];
    accentColor: [number, number, number];
    darkColor: [number, number, number];
    lightGray: [number, number, number];
    mediumGray: [number, number, number];
    font: string;
    currency: string;
    taxLabels: { cgst: string; sgst: string; };
  }>;
  private primaryColor: [number, number, number];
  private accentColor: [number, number, number];
  private darkColor: [number, number, number];
  private lightGray: [number, number, number];
  private mediumGray: [number, number, number];
  private font: string;
  private currency: string;
  private taxLabels: { cgst: string; sgst: string; };

  /**
   * @param config Optional configuration for colors, fonts, and currency
   * @param templateId Optional template ID to use for styling
   * @param template Optional template object to use directly
   */
  constructor(config: ProfessionalBillGenerator['config'] = {}, templateId?: string, template?: Template) {
    // Set default paper size and orientation
    let paperSize: PaperSize = 'a4';
    let orientation: PaperOrientation = 'portrait';

    // Use provided template if available
    if (template) {
      // Use template settings
      this.useTemplateSettings(template);
    } else if (templateId && templateCache[templateId]) {
      // Use cached template if available
      this.useTemplateSettings(templateCache[templateId]);
    } else {
      // Use default template or fallback settings
      if (defaultTemplateCache) {
        this.useTemplateSettings(defaultTemplateCache);
      } else {
        // Use default settings
        this.config = {
          primaryColor: [41, 128, 185],
          accentColor: [39, 174, 96],
          darkColor: [44, 62, 80],
          lightGray: [236, 240, 241],
          mediumGray: [149, 165, 166],
          font: 'helvetica',
          currency: '₹',
          taxLabels: {
            cgst: 'CGST',
            sgst: 'SGST'
          }
        };
      }
    }

    // Get paper dimensions
    const dimensions = getPaperDimensions(paperSize, orientation);
    
    // Create PDF with appropriate size and orientation
    this.doc = new jsPDF(
      orientation === 'portrait' ? 'p' : 'l',
      'mm',
      [dimensions.width, dimensions.height]
    );

    this.pageWidth = this.doc.internal.pageSize.width;
    this.pageHeight = this.doc.internal.pageSize.height;

    if (template) {
      this.margin = template.layout.margin;
      this.currentY = template.layout.margin + 4;
      this.primaryColor = template.colors.primary as [number, number, number];
      this.accentColor = template.colors.accent as [number, number, number];
      this.darkColor = template.colors.dark as [number, number, number];
      this.lightGray = template.colors.lightGray as [number, number, number];
      this.mediumGray = template.colors.mediumGray as [number, number, number];
      this.currency = template.settings.currency;
    } else {
      this.margin = 6;
      this.currentY = 10;
      this.primaryColor = config.primaryColor ?? [41, 128, 185];
      this.accentColor = config.accentColor ?? [39, 174, 96];
      this.darkColor = config.darkColor ?? [44, 62, 80];
      this.lightGray = config.lightGray ?? [236, 240, 241];
      this.mediumGray = config.mediumGray ?? [149, 165, 166];
      this.currency = config.currency ?? '₹';
    }
    
    this.config = config;
    this.font = config.font ?? 'helvetica';
    this.taxLabels = config.taxLabels ?? { cgst: 'CGST (1.5%)', sgst: 'SGST (1.5%)' };
  }

  private setFillColor(color: [number, number, number]): void {
    this.doc.setFillColor(color[0], color[1], color[2]);
  }
  private setTextColor(color: [number, number, number]): void {
    this.doc.setTextColor(color[0], color[1], color[2]);
  }
  private setDrawColor(color: [number, number, number]): void {
    this.doc.setDrawColor(color[0], color[1], color[2]);
  }

  /**
   * Apply template settings to the generator
   */
  private useTemplateSettings(template: Template) {
    this.config = {
      primaryColor: template.colors.primary as [number, number, number],
      accentColor: template.colors.accent as [number, number, number],
      darkColor: template.colors.dark as [number, number, number],
      lightGray: template.colors.lightGray as [number, number, number],
      mediumGray: template.colors.mediumGray as [number, number, number],
      font: 'helvetica',
      currency: template.settings.currency || '₹',
      taxLabels: {
        cgst: 'CGST',
        sgst: 'SGST'
      }
    };
  }

  /**
   * Generates the professional bill PDF from invoice data.
   * @param data InvoiceData object
   */
  generateProfessionalBill(data: InvoiceData): void {
    try {
      if (!data?.invoice || !data?.customer || !Array.isArray(data?.items)) {
        throw new Error('Invalid invoice data structure');
      }
      this.addProfessionalHeader(data);
      this.addBusinessInformation(data);
      this.addInvoiceDetails(data);
      this.addItemizedTable(data);
      this.addSummaryAndGST(data);
      this.addFooterSection(data);
    } catch (error: any) {
      // Add error message to PDF
      this.doc.setTextColor(255, 0, 0);
      this.doc.setFontSize(16);
      this.doc.setFont(this.font, 'bold');
      this.doc.text('Error generating invoice', this.pageWidth/2, this.pageHeight/2, { align: 'center' });
      this.doc.setFontSize(12);
      this.doc.setFont(this.font, 'normal');
      this.doc.text('Please contact support for assistance', this.pageWidth/2, this.pageHeight/2 + 10, { align: 'center' });
      throw error;
    }
  }

  private addProfessionalHeader(data: InvoiceData): void {
    // Compact header background
    this.setFillColor(this.primaryColor);
    this.doc.rect(0, 0, this.pageWidth, 28, 'F');
    
    // Company name - smaller font
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'JEWELRY HOUSE', this.margin, 14);
    
    // Tagline - smaller
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text('Premium Jewelry & Ornaments', this.margin, 20);
    
    // TAX INVOICE - smaller
    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TAX INVOICE', this.pageWidth - this.margin, 12, { align: 'right' });
    
    // Invoice number and status on same line
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`Invoice #${data.invoice.invoiceNumber}`, this.pageWidth - this.margin, 18, { align: 'right' });
    
    // Compact status badge
    const statusColor = data.invoice.status === 'paid' ? this.accentColor : [231, 76, 60] as [number, number, number];
    this.doc.setFillColor(statusColor[0], statusColor[1], statusColor[2]);
    this.doc.roundedRect(this.pageWidth - this.margin - 20, 21, 20, 5, 1, 1, 'F');
    this.doc.setFontSize(7);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.invoice.status.toUpperCase(), this.pageWidth - this.margin - 10, 24.5, { align: 'center' });
    
    this.currentY = 32;
  }

  private addBusinessInformation(data: InvoiceData): void {
    // Compact business information section
    this.setFillColor(this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 32, 'F');
    
    // Border
    this.setDrawColor(this.mediumGray);
    this.doc.setLineWidth(0.3);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 32, 'D');
    
    // From section - compact
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('FROM:', this.margin + 3, this.currentY + 6);
    
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'Company Name', this.margin + 3, this.currentY + 12);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(7);
    
    // Compact company info
    const companyInfo = `${data.company?.address || 'Address'} | Ph: ${data.company?.phone || 'N/A'} | GSTIN: ${data.company?.gstin || 'N/A'}`;
    const companyLines = splitText(companyInfo, 70);
    let yPos = this.currentY + 16;
    companyLines.slice(0, 2).forEach(line => { // Limit to 2 lines
      this.doc.text(line, this.margin + 3, yPos);
      yPos += 3.5;
    });
    
    // Bill To section - compact
    const rightX = this.pageWidth / 2 + 5;
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('BILL TO:', rightX, this.currentY + 6);
    
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.customer.name, rightX, this.currentY + 12);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(7);
    
    // Compact customer info
    const customerInfo = `${data.customer.address} | Ph: ${data.customer.phone} | ${data.customer.gstin ? 'GSTIN: ' + data.customer.gstin : ''}`;
    const customerLines = splitText(customerInfo, 70);
    yPos = this.currentY + 16;
    customerLines.slice(0, 2).forEach(line => { // Limit to 2 lines
      this.doc.text(line, rightX, yPos);
      yPos += 3.5;
    });
    
    this.currentY += 36;
  }

  private addInvoiceDetails(data: InvoiceData): void {
    // Compact invoice details banner
    this.setFillColor(this.primaryColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 14, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    
    // Single line with key details
    this.doc.text(`Date: ${data.invoice.invoiceDate}`, this.margin + 3, this.currentY + 5);
    this.doc.text(`Due: ${data.invoice.dueDate}`, this.margin + 3, this.currentY + 10);
    
    // Center - Metal rates (compact)
    if (data.metalRates) {
      const centerX = this.pageWidth / 2;
      this.doc.text(`Gold: ₹${data.metalRates.gold}/g`, centerX - 15, this.currentY + 5);
      this.doc.text(`Silver: ₹${data.metalRates.silver}/g`, centerX - 15, this.currentY + 10);
    }
    
    // Right side - compact
    this.doc.text(`Terms: ${data.invoice.paymentTerms || '30 Days'}`, this.pageWidth - this.margin - 3, this.currentY + 5, { align: 'right' });
    if (data.invoice.ewayBillNumber) {
      this.doc.text(`E-Way: ${data.invoice.ewayBillNumber}`, this.pageWidth - this.margin - 3, this.currentY + 10, { align: 'right' });
    }
    
    this.currentY += 18;
  }

  private addItemizedTable(data: InvoiceData): void {
    // Compact table title
    this.setFillColor(this.darkColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 6, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('ITEMIZED JEWELRY DETAILS', this.margin + 2, this.currentY + 4);

    this.currentY += 8;

    // Compact table with reduced spacing
    const rowHeight = 14; // Reduced height for single page fit
    const headerHeight = 10;

    // Compact column definitions for single page fit
    const columns = [
      { header: '#', width: 6, align: 'center' },
      { header: 'Item Description', width: 35, align: 'left' },
      { header: 'HSN', width: 10, align: 'center' },
      { header: 'Qty', width: 8, align: 'center' },
      { header: 'Metal', width: 14, align: 'center' },
      { header: 'Weights', width: 24, align: 'center' },
      { header: 'Rate/g', width: 16, align: 'right' },
      { header: 'Making', width: 14, align: 'right' },
      { header: 'Stone', width: 12, align: 'right' },
      { header: 'Total', width: 18, align: 'right' }
    ];

    // Enhanced table header
    this.doc.setFillColor(52, 73, 94);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, headerHeight, 'F');

    // Header borders
    this.doc.setDrawColor(0, 0, 0);
    this.doc.setLineWidth(0.8);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, headerHeight, 'D');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(7);
    this.doc.setFont('helvetica', 'bold');

    let xPos = this.margin;
    columns.forEach((col, index) => {
      // Draw vertical separators
      if (index > 0) {
        this.doc.setDrawColor(255, 255, 255);
        this.doc.setLineWidth(0.3);
        this.doc.line(xPos, this.currentY, xPos, this.currentY + headerHeight);
      }

      const textX = col.align === 'center' ? xPos + col.width / 2 :
                   col.align === 'right' ? xPos + col.width - 1 : xPos + 1;
      this.doc.text(col.header, textX, this.currentY + 7, { align: col.align as any });
      xPos += col.width;
    });

    this.currentY += headerHeight;

    // Enhanced table rows with better formatting
    data.items.forEach((item, index) => {
      // Alternating row colors for better readability
      if (index % 2 === 0) {
        this.doc.setFillColor(248, 249, 250);
        this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'F');
      }

      // Row border with better visibility
      this.doc.setDrawColor(180, 180, 180);
      this.doc.setLineWidth(0.5);
      this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'D');

      this.doc.setTextColor(0, 0, 0);
      this.doc.setFontSize(6);
      this.doc.setFont('helvetica', 'normal');

      xPos = this.margin;

      columns.forEach((col, colIndex) => {
        // Draw vertical separators with better visibility
        if (colIndex > 0) {
          this.doc.setDrawColor(180, 180, 180);
          this.doc.setLineWidth(0.3);
          this.doc.line(xPos, this.currentY, xPos, this.currentY + rowHeight);
        }

        const textX = col.align === 'center' ? xPos + col.width / 2 :
                     col.align === 'right' ? xPos + col.width - 2 : xPos + 2;

        // Enhanced content formatting for each column
        switch (colIndex) {
          case 0: // Serial number
            this.doc.setFont('helvetica', 'bold');
            this.doc.text((index + 1).toString(), textX, this.currentY + 12, { align: 'center' });
            this.doc.setFont('helvetica', 'normal');
            break;

          case 1: // Item description - compact layout
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(7);
            this.doc.text(truncateText(item.itemName || 'Gold Ring', 22), xPos + 1, this.currentY + 5);

            this.doc.setFont('helvetica', 'normal');
            this.doc.setFontSize(5);
            this.doc.text(`Code: ${item.itemId || 'GR001'}`, xPos + 1, this.currentY + 9);
            this.doc.text(`Premium Quality`, xPos + 1, this.currentY + 12);
            this.doc.setFontSize(6);
            break;

          case 2: // HSN
            this.doc.setFont('helvetica', 'bold');
            this.doc.text(item.hsnCode || '7113', textX, this.currentY + 12, { align: 'center' });
            this.doc.setFont('helvetica', 'normal');
            break;

          case 3: // Quantity
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(8);
            this.doc.text((item.quantity || 1).toString(), textX, this.currentY + 12, { align: 'center' });
            this.doc.setFont('helvetica', 'normal');
            this.doc.setFontSize(7);
            break;

          case 4: // Metal/Purity - compact
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(6);
            this.doc.text(`${item.metalType || 'Gold'}`, textX, this.currentY + 6, { align: 'center' });
            this.doc.setFontSize(5);
            this.doc.text(`${item.purity || '22K'}`, textX, this.currentY + 10, { align: 'center' });
            this.doc.setFont('helvetica', 'normal');
            this.doc.setFontSize(6);
            break;

          case 5: // Weights - ultra compact
            this.doc.setFontSize(5);
            const leftX = xPos + 1;
            const rightX = xPos + col.width - 1;

            // Compact weight display
            this.doc.text(`G:${parseFloat(item.grossWeight || '0').toFixed(2)}`, leftX, this.currentY + 4);
            this.doc.text(`N:${parseFloat(item.netWeight || '0').toFixed(2)}`, leftX, this.currentY + 7);
            this.doc.text(`S:${parseFloat(item.stoneWeight || '0').toFixed(2)}`, leftX, this.currentY + 10);
            this.doc.text(`P:${item.purityPercentage || '91.6'}%`, leftX, this.currentY + 13);
            this.doc.setFontSize(6);
            break;

          case 6: // Rate per gram - compact
            const ratePerGram = parseFloat(item.ratePerGram || '0');
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(6);
            this.doc.text(`₹${ratePerGram.toLocaleString('en-IN')}`, textX, this.currentY + 8, { align: 'right' });
            this.doc.setFont('helvetica', 'normal');
            break;

          case 7: // Making charges - compact
            const makingCharges = parseFloat(item.makingCharges || '0');
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(6);
            this.doc.text(`₹${makingCharges.toLocaleString('en-IN')}`, textX, this.currentY + 8, { align: 'right' });
            this.doc.setFont('helvetica', 'normal');
            break;

          case 8: // Stone charges - compact
            const stoneCharges = parseFloat(item.stoneCharges || '0');
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(6);
            this.doc.text(`₹${stoneCharges.toLocaleString('en-IN')}`, textX, this.currentY + 8, { align: 'right' });
            this.doc.setFont('helvetica', 'normal');
            break;

          case 9: // Total amount - compact but highlighted
            this.doc.setFont('helvetica', 'bold');
            this.doc.setFontSize(7);
            this.doc.setTextColor(39, 174, 96);
            const totalAmount = parseFloat(item.totalAmount || '0');
            this.doc.text(`₹${totalAmount.toLocaleString('en-IN')}`, textX, this.currentY + 8, { align: 'right' });
            this.doc.setTextColor(0, 0, 0);
            this.doc.setFont('helvetica', 'normal');
            this.doc.setFontSize(6);
            break;
        }

        xPos += col.width;
      });

      this.currentY += rowHeight;
    });

    this.currentY += 5;
  }

  private addSummaryAndGST(data: InvoiceData): void {
    // Compact Summary and GST section
    const summaryWidth = 65;
    const summaryX = this.pageWidth - this.margin - summaryWidth;

    // Compact Invoice Summary Box
    this.doc.setFillColor(240, 248, 255);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 35, 'F');

    this.setDrawColor(this.primaryColor);
    this.doc.setLineWidth(0.5);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 35, 'D');

    // Compact summary header
    this.setFillColor(this.primaryColor);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 6, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('INVOICE SUMMARY', summaryX + summaryWidth/2, this.currentY + 4, { align: 'center' });

    // Compact summary items
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(7);
    this.doc.setFont('helvetica', 'normal');

    let summaryY = this.currentY + 12;

    // Compact subtotal
    const subtotal = parseFloat(data.invoice.subtotal || '0');
    this.doc.text('Subtotal:', summaryX + 2, summaryY);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(`${this.currency}${subtotal.toLocaleString('en-IN')}`, summaryX + summaryWidth - 2, summaryY, { align: 'right' });
    this.doc.setFont('helvetica', 'normal');
    summaryY += 4;

    // CGST
    const cgstAmount = parseFloat(data.invoice.cgstAmount || '0');
    this.doc.text('CGST @ 1.5%:', summaryX + 2, summaryY);
    this.doc.text(`${this.currency}${cgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 2, summaryY, { align: 'right' });
    summaryY += 4;

    // SGST
    const sgstAmount = parseFloat(data.invoice.sgstAmount || '0');
    this.doc.text('SGST @ 1.5%:', summaryX + 2, summaryY);
    this.doc.text(`${this.currency}${sgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 2, summaryY, { align: 'right' });
    summaryY += 4;

    // Total line
    this.setDrawColor(this.primaryColor);
    this.doc.setLineWidth(0.5);
    this.doc.line(summaryX + 2, summaryY, summaryX + summaryWidth - 2, summaryY);
    summaryY += 3;

    // Total amount
    const totalAmount = parseFloat(data.invoice.totalAmount || '0');
    this.doc.setFont('helvetica', 'bold');
    this.doc.setFontSize(9);
    this.setTextColor(this.accentColor);
    this.doc.text('TOTAL:', summaryX + 2, summaryY);
    this.doc.text(`${this.currency}${totalAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 2, summaryY, { align: 'right' });

    // Skip GST breakdown table - not required
    this.currentY += 10;
  }



  private addFooterSection(data: InvoiceData): void {
    // Compact amount in words
    this.setFillColor(this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 8, 'F');
    
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(7);
    this.doc.setFont('helvetica', 'bold');
    
    const wordsAmount = parseFloat(data.invoice.totalAmount || '0');
    const amountInWords = convertToWords(wordsAmount);
    this.doc.text('Amount in Words:', this.margin + 2, this.currentY + 3);
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(6);
    this.doc.text(amountInWords, this.margin + 2, this.currentY + 6.5);
    
    this.currentY += 12;
    
    // Ultra compact footer information in two columns
    const leftColWidth = (this.pageWidth - 3 * this.margin) / 2;
    
    // Compact bank details
    if (data.company?.bankDetails) {
      this.doc.setFillColor(255, 255, 255);
      this.setDrawColor(this.mediumGray);
      this.doc.setLineWidth(0.3);
      this.doc.rect(this.margin, this.currentY, leftColWidth, 18, 'FD');
      
      this.setTextColor(this.primaryColor);
      this.doc.setFontSize(7);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text('BANK DETAILS', this.margin + 2, this.currentY + 5);
      
      this.setTextColor(this.darkColor);
      this.doc.setFontSize(5);
      this.doc.setFont('helvetica', 'normal');
      
      let bankY = this.currentY + 8;
      this.doc.text(`${data.company.bankDetails.bankName} | A/c: ${data.company.bankDetails.accountNumber}`, this.margin + 2, bankY);
      bankY += 3;
      this.doc.text(`IFSC: ${data.company.bankDetails.ifscCode} | ${data.company.bankDetails.branch}`, this.margin + 2, bankY);
    }
    
    // Compact terms and signature
    const rightColX = this.margin + leftColWidth + this.margin;
    this.doc.setFillColor(255, 255, 255);
    this.setDrawColor(this.mediumGray);
    this.doc.setLineWidth(0.3);
    this.doc.rect(rightColX, this.currentY, leftColWidth, 18, 'FD');
    
    this.setTextColor(this.primaryColor);
    this.doc.setFontSize(7);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TERMS & CONDITIONS', rightColX + 2, this.currentY + 5);
    
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(5);
    this.doc.setFont('helvetica', 'normal');
    
    let termsY = this.currentY + 8;
    this.doc.text('• Payment due within 30 days • Goods once sold will not be taken back', rightColX + 2, termsY);
    termsY += 3;
    this.doc.text('• Subject to local jurisdiction', rightColX + 2, termsY);
    
    // Compact signature
    this.doc.setFontSize(6);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Authorized Signatory', rightColX + leftColWidth - 2, this.currentY + 15, { align: 'right' });
    
    // Compact footer line - positioned relative to current content
    this.currentY += 22;
    this.setDrawColor(this.primaryColor);
    this.doc.setLineWidth(1);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    
    // Compact thank you message
    this.setTextColor(this.primaryColor);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Thank you for your business!', this.pageWidth/2, this.currentY + 5, { align: 'center' });
    
    // Compact contact info
    this.doc.setFontSize(6);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`${data.company?.phone || ''} | ${data.company?.email || ''} | ${data.company?.website || ''}`, 
                  this.pageWidth/2, this.currentY + 8, { align: 'center' });
  }

  save(filename: string = 'professional-jewelry-bill.pdf'): void {
    this.doc.save(filename);
  }

  print(): void {
    if (typeof window !== 'undefined') {
      window.open(this.doc.output('bloburl'), '_blank');
    } else {
      console.log('PDF preview is not available in this environment.');
    }
  }
}

// Export functions
export function generateProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config'], templateId?: string): void {
  const generator = new ProfessionalBillGenerator(config, templateId);
  generator.generateProfessionalBill(data);
  generator.save(`Professional-Bill-${data.invoice.invoiceNumber}.pdf`);
}

export function printProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config'], templateId?: string): void {
  const generator = new ProfessionalBillGenerator(config, templateId);
  generator.generateProfessionalBill(data);
  generator.print();
}

// Template-specific functions
export function generateBillWithTemplate(data: InvoiceData, templateId: string): void {
  const generator = new ProfessionalBillGenerator({}, templateId);
  generator.generateProfessionalBill(data);
  generator.save(`Professional-Bill-${data.invoice.invoiceNumber}.pdf`);
}

export function previewBillWithTemplate(data: InvoiceData, templateId: string): void {
  const generator = new ProfessionalBillGenerator({}, templateId);
  generator.generateProfessionalBill(data);
  generator.print();
}

// New function to generate bill with template object directly
export function generateBillWithTemplateObject(data: InvoiceData, template: Template): void {
  const generator = new ProfessionalBillGenerator({}, undefined, template);
  generator.generateProfessionalBill(data);
  generator.save(`Professional-Bill-${data.invoice.invoiceNumber}.pdf`);
}

// Function to generate bill with template ID (async version that fetches template)
export async function generateBillWithTemplateId(data: InvoiceData, templateId: string): Promise<void> {
  try {
    const template = await fetchTemplateById(templateId);
    if (template) {
      generateBillWithTemplateObject(data, template);
    } else {
      // Fallback to default template
      const defaultTemplate = await fetchDefaultTemplate();
      if (defaultTemplate) {
        generateBillWithTemplateObject(data, defaultTemplate);
      } else {
        // Fallback to basic generation
        generateProfessionalBill(data);
      }
    }
  } catch (error) {
    console.error('Error generating bill with template:', error);
    // Fallback to basic generation
    generateProfessionalBill(data);
  }
}