import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface InvoiceData {
  invoice: {
    id: number;
    invoiceNumber: string;
    invoiceDate: string;
    dueDate: string;
    subtotal: string;
    cgstAmount: string;
    sgstAmount: string;
    igstAmount: string;
    discountAmount: string;
    totalAmount: string;
    paidAmount: string;
    balanceAmount: string;
    notes?: string;
    status: string;
    ewayBillNumber?: string;
    paymentTerms?: string;
  };
  customer: {
    id: number;
    name: string;
    email: string;
    phone: string;
    address: string;
    gstin?: string;
    pan?: string;
    stateCode?: string;
  };
  items: Array<{
    id: number;
    itemId: number;
    itemName: string;
    quantity: number;
    grossWeight: string;
    netWeight: string;
    fineWeight?: string;
    stoneWeight?: string;
    stoneAmount?: string;
    purityPercentage?: string;
    wastagePercentage?: string;
    ratePerGram: string;
    goldValue?: string;
    makingCharges?: string;
    stoneCharges?: string;
    itemTotal: string;
    taxableAmount?: string;
    cgstAmount?: string;
    sgstAmount?: string;
    totalAmount: string;
    metalType?: string;
    purity?: string;
    hsnCode?: string;
  }>;
  company?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    gstin: string;
    pan?: string;
    stateCode?: string;
    website?: string;
    bankDetails?: {
      bankName: string;
      accountNumber: string;
      ifscCode: string;
      branch: string;
    };
  };
  metalRates?: {
    gold: number;
    silver: number;
    date: string;
  };
}

/**
 * Utility functions for text processing and number-to-words conversion
 */
function splitText(text: string | undefined, maxWidth: number): string[] {
  if (!text) return ['N/A'];
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';
  words.forEach(word => {
    if ((currentLine + word).length <= maxWidth) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  });
  if (currentLine) lines.push(currentLine);
  return lines;
}

function truncateText(text: string | undefined, maxLength: number): string {
  if (!text) return 'N/A';
  return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
}

function convertToWords(amount: number): string {
  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  if (amount === 0) return 'Zero Rupees Only';
  const rupees = Math.floor(amount);
  const paise = Math.round((amount - rupees) * 100);
  let result = convertNumberToWords(rupees, ones, teens, tens);
  if (result) result += ' Rupees';
  if (paise > 0) {
    result += (result ? ' and ' : '') + convertNumberToWords(paise, ones, teens, tens) + ' Paise';
  }
  return result + ' Only';
}

function convertNumberToWords(num: number, ones: string[], teens: string[], tens: string[]): string {
  if (num === 0) return '';
  if (num < 10) return ones[num];
  if (num < 20) return teens[num - 10];
  if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 ? ' ' + ones[num % 10] : '');
  if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 ? ' ' + convertNumberToWords(num % 100, ones, teens, tens) : '');
  if (num < 100000) return convertNumberToWords(Math.floor(num / 1000), ones, teens, tens) + ' Thousand' + (num % 1000 ? ' ' + convertNumberToWords(num % 1000, ones, teens, tens) : '');
  if (num < 10000000) return convertNumberToWords(Math.floor(num / 100000), ones, teens, tens) + ' Lakh' + (num % 100000 ? ' ' + convertNumberToWords(num % 100000, ones, teens, tens) : '');
  return convertNumberToWords(Math.floor(num / 10000000), ones, teens, tens) + ' Crore' + (num % 10000000 ? ' ' + convertNumberToWords(num % 10000000, ones, teens, tens) : '');
}

/**
 * Enhanced ProfessionalBillGenerator for robust PDF invoice generation.
 * Supports page breaks, configuration, and improved error handling.
 */
class ProfessionalBillGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number;
  private currentY: number;
  private config: Partial<{
    primaryColor: [number, number, number];
    accentColor: [number, number, number];
    darkColor: [number, number, number];
    lightGray: [number, number, number];
    mediumGray: [number, number, number];
    font: string;
    currency: string;
    taxLabels: { cgst: string; sgst: string; };
  }>;
  private primaryColor: [number, number, number];
  private accentColor: [number, number, number];
  private darkColor: [number, number, number];
  private lightGray: [number, number, number];
  private mediumGray: [number, number, number];
  private font: string;
  private currency: string;
  private taxLabels: { cgst: string; sgst: string; };

  /**
   * @param config Optional configuration for colors, fonts, and currency
   */
  constructor(config: ProfessionalBillGenerator['config'] = {}) {
    this.doc = new jsPDF('p', 'mm', 'a4');
    this.pageWidth = this.doc.internal.pageSize.width;
    this.pageHeight = this.doc.internal.pageSize.height;
    this.margin = 8; // Reduced margin from 15 to 8
    this.currentY = 15;
    this.config = config;
    this.primaryColor = config.primaryColor ?? [41, 128, 185];
    this.accentColor = config.accentColor ?? [39, 174, 96];
    this.darkColor = config.darkColor ?? [44, 62, 80];
    this.lightGray = config.lightGray ?? [236, 240, 241];
    this.mediumGray = config.mediumGray ?? [149, 165, 166];
    this.font = config.font ?? 'helvetica';
    this.currency = config.currency ?? '₹';
    this.taxLabels = config.taxLabels ?? { cgst: 'CGST (1.5%)', sgst: 'SGST (1.5%)' };
  }

  private setFillColor(color: [number, number, number]): void {
    this.doc.setFillColor(color[0], color[1], color[2]);
  }
  private setTextColor(color: [number, number, number]): void {
    this.doc.setTextColor(color[0], color[1], color[2]);
  }
  private setDrawColor(color: [number, number, number]): void {
    this.doc.setDrawColor(color[0], color[1], color[2]);
  }

  /**
   * Generates the professional bill PDF from invoice data.
   * @param data InvoiceData object
   */
  generateProfessionalBill(data: InvoiceData): void {
    try {
      if (!data?.invoice || !data?.customer || !Array.isArray(data?.items)) {
        throw new Error('Invalid invoice data structure');
      }
      this.addProfessionalHeader(data);
      this.addBusinessInformation(data);
      this.addInvoiceDetails(data);
      this.addItemizedTable(data);
      this.addSummaryAndGST(data);
      this.addFooterSection(data);
    } catch (error: any) {
      // Add error message to PDF
      this.doc.setTextColor(255, 0, 0);
      this.doc.setFontSize(16);
      this.doc.setFont(this.font, 'bold');
      this.doc.text('Error generating invoice', this.pageWidth/2, this.pageHeight/2, { align: 'center' });
      this.doc.setFontSize(12);
      this.doc.setFont(this.font, 'normal');
      this.doc.text('Please contact support for assistance', this.pageWidth/2, this.pageHeight/2 + 10, { align: 'center' });
      throw error;
    }
  }

  private addProfessionalHeader(data: InvoiceData): void {
    // Header background
    this.setFillColor(this.primaryColor);
    this.doc.rect(0, 0, this.pageWidth, 40, 'F');
    
    // Company name
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(22);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'JEWELRY HOUSE', this.margin, 18);
    
    // Tagline
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text('Premium Jewelry & Ornaments', this.margin, 25);
    
    // TAX INVOICE
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TAX INVOICE', this.pageWidth - this.margin, 18, { align: 'right' });
    
    // Invoice number
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`Invoice #${data.invoice.invoiceNumber}`, this.pageWidth - this.margin, 28, { align: 'right' });
    
    // Status badge
    const statusColor = data.invoice.status === 'paid' ? this.accentColor : [231, 76, 60] as [number, number, number];
    this.doc.setFillColor(statusColor[0], statusColor[1], statusColor[2]);
    this.doc.roundedRect(this.pageWidth - this.margin - 25, 30, 25, 6, 2, 2, 'F');
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.invoice.status.toUpperCase(), this.pageWidth - this.margin - 12.5, 34, { align: 'center' });
    
    this.currentY = 50;
  }

  private addBusinessInformation(data: InvoiceData): void {
    // Business information section
    this.setFillColor(this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 45, 'F');
    
    // Border
    this.setDrawColor(this.mediumGray);
    this.doc.setLineWidth(0.5);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 45, 'D');
    
    // From section
    this.setTextColor(this.darkColor);
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('FROM:', this.margin + 5, this.currentY + 10);
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.company?.name || 'Company Name', this.margin + 5, this.currentY + 18);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(9);
    
    // Company address
    const companyAddress = splitText(data.company?.address, 50);
    let yPos = this.currentY + 24;
    companyAddress.forEach(line => {
      this.doc.text(line, this.margin + 5, yPos);
      yPos += 4;
    });
    
    // Company contact
    this.doc.text(`Phone: ${data.company?.phone || 'N/A'}`, this.margin + 5, yPos);
    this.doc.text(`Email: ${data.company?.email || 'N/A'}`, this.margin + 5, yPos + 4);
    this.doc.text(`GSTIN: ${data.company?.gstin || 'N/A'}`, this.margin + 5, yPos + 8);
    
    // Bill To section
    const rightX = this.pageWidth / 2 + 10;
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('BILL TO:', rightX, this.currentY + 10);
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(data.customer.name, rightX, this.currentY + 18);
    
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(9);
    
    // Customer address
    const customerAddress = splitText(data.customer.address, 50);
    yPos = this.currentY + 24;
    customerAddress.forEach(line => {
      this.doc.text(line, rightX, yPos);
      yPos += 4;
    });
    
    // Customer contact
    this.doc.text(`Phone: ${data.customer.phone}`, rightX, yPos);
    this.doc.text(`Email: ${data.customer.email}`, rightX, yPos + 4);
    if (data.customer.gstin) {
      this.doc.text(`GSTIN: ${data.customer.gstin}`, rightX, yPos + 8);
    }
    
    this.currentY += 55;
  }

  private addInvoiceDetails(data: InvoiceData): void {
    // Invoice details banner
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 20, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    
    // Left side details
    this.doc.text(`Invoice Date: ${data.invoice.invoiceDate}`, this.margin + 5, this.currentY + 8);
    this.doc.text(`Due Date: ${data.invoice.dueDate}`, this.margin + 5, this.currentY + 15);
    
    // Center - Metal rates
    if (data.metalRates) {
      const centerX = this.pageWidth / 2;
      this.doc.text(`Gold: ₹${data.metalRates.gold}/g`, centerX - 20, this.currentY + 8);
      this.doc.text(`Silver: ₹${data.metalRates.silver}/g`, centerX - 20, this.currentY + 15);
    }
    
    // Right side
    this.doc.text(`Payment Terms: ${data.invoice.paymentTerms || '30 Days'}`, this.pageWidth - this.margin - 5, this.currentY + 8, { align: 'right' });
    if (data.invoice.ewayBillNumber) {
      this.doc.text(`E-Way Bill: ${data.invoice.ewayBillNumber}`, this.pageWidth - this.margin - 5, this.currentY + 15, { align: 'right' });
    }
    
    this.currentY += 30;
  }

  private addItemizedTable(data: InvoiceData): void {
    // Table title
    this.doc.setFillColor(...this.darkColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 8, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('ITEMIZED JEWELRY DETAILS', this.margin + 2, this.currentY + 5.5);

    this.currentY += 12;

    // Prepare table data for autoTable
    const tableData = data.items.map((item, index) => [
      (index + 1).toString(),
      `${item.itemName || 'Jewelry Item'}\nCode: ${item.itemId || 'N/A'}`,
      item.hsnCode || '7113',
      (item.quantity || 1).toString(),
      `${item.metalType || 'Gold'}\n${item.purity || '22K'}`,
      `G: ${parseFloat(item.grossWeight || '0').toFixed(3)}\nN: ${parseFloat(item.netWeight || '0').toFixed(3)}\nS: ${parseFloat(item.stoneWeight || '0').toFixed(3)}`,
      `₹${parseFloat(item.ratePerGram || '0').toLocaleString('en-IN')}`,
      `₹${parseFloat(item.makingCharges || '0').toLocaleString('en-IN')}`,
      `₹${parseFloat(item.stoneCharges || '0').toLocaleString('en-IN')}`,
      `₹${parseFloat(item.totalAmount || '0').toLocaleString('en-IN')}`
    ]);

    // Use autoTable for professional table formatting
    this.doc.autoTable({
      startY: this.currentY,
      head: [['#', 'Item Description', 'HSN', 'Qty', 'Metal/Purity', 'Weights (g)', 'Rate/g', 'Making', 'Stone', 'Total']],
      body: tableData,
      theme: 'grid',
      headStyles: {
        fillColor: [52, 73, 94],
        textColor: [255, 255, 255],
        fontSize: 7,
        fontStyle: 'bold',
        halign: 'center',
        valign: 'middle'
      },
      bodyStyles: {
        fontSize: 6,
        cellPadding: 1.5,
        valign: 'middle'
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 8 },
        1: { halign: 'left', cellWidth: 40 },
        2: { halign: 'center', cellWidth: 12 },
        3: { halign: 'center', cellWidth: 8 },
        4: { halign: 'center', cellWidth: 18 },
        5: { halign: 'center', cellWidth: 22 },
        6: { halign: 'right', cellWidth: 18 },
        7: { halign: 'right', cellWidth: 15 },
        8: { halign: 'right', cellWidth: 15 },
        9: { halign: 'right', cellWidth: 22, fontStyle: 'bold', textColor: [39, 174, 96] }
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250]
      },
      margin: { left: this.margin, right: this.margin },
      didDrawPage: (data: any) => {
        // Update currentY after table is drawn
        this.currentY = data.cursor?.y || this.currentY;
      }
    });

    // Update currentY to position after the table
    this.currentY = (this.doc as any).lastAutoTable.finalY + 10;
  }

  private addSummaryAndGST(data: InvoiceData): void {
    // Combined Summary and GST section
    const summaryWidth = 80;
    const summaryX = this.pageWidth - this.margin - summaryWidth;

    // Invoice Summary Box
    this.doc.setFillColor(240, 248, 255);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 50, 'F');

    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(1);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 50, 'D');

    // Summary header
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(summaryX, this.currentY, summaryWidth, 8, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('INVOICE SUMMARY', summaryX + summaryWidth/2, this.currentY + 5.5, { align: 'center' });

    // Summary items
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'normal');

    let summaryY = this.currentY + 16;

    // Subtotal
    const subtotal = parseFloat(data.invoice.subtotal || '0');
    this.doc.text('Subtotal:', summaryX + 3, summaryY);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(`${this.currency}${subtotal.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    this.doc.setFont('helvetica', 'normal');
    summaryY += 6;

    // CGST
    const cgstAmount = parseFloat(data.invoice.cgstAmount || '0');
    this.doc.text('CGST @ 1.5%:', summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${cgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    summaryY += 6;

    // SGST
    const sgstAmount = parseFloat(data.invoice.sgstAmount || '0');
    this.doc.text('SGST @ 1.5%:', summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${sgstAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });
    summaryY += 6;

    // Total line
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(0.8);
    this.doc.line(summaryX + 3, summaryY, summaryX + summaryWidth - 3, summaryY);
    summaryY += 4;

    // Total amount
    const totalAmount = parseFloat(data.invoice.totalAmount || '0');
    this.doc.setFont('helvetica', 'bold');
    this.doc.setFontSize(12);
    this.doc.setTextColor(...this.accentColor);
    this.doc.text('GRAND TOTAL:', summaryX + 3, summaryY);
    this.doc.text(`${this.currency}${totalAmount.toLocaleString('en-IN')}`, summaryX + summaryWidth - 3, summaryY, { align: 'right' });

    // GST Breakdown Table using autoTable
    this.currentY += 60;

    // GST table title
    this.doc.setFillColor(...this.darkColor);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 8, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('GST BREAKDOWN', this.margin + 2, this.currentY + 5.5);

    this.currentY += 12;

    // GST data
    const totalTax = cgstAmount + sgstAmount;
    const gstData = [
      ['7113', 'Jewelry Items', `₹${subtotal.toLocaleString('en-IN')}`, `₹${cgstAmount.toLocaleString('en-IN')}`, `₹${sgstAmount.toLocaleString('en-IN')}`, `₹${totalTax.toLocaleString('en-IN')}`]
    ];

    // Use autoTable for GST breakdown
    this.doc.autoTable({
      startY: this.currentY,
      head: [['HSN/SAC', 'Description', 'Taxable Value', 'CGST (1.5%)', 'SGST (1.5%)', 'Total Tax']],
      body: gstData,
      theme: 'grid',
      headStyles: {
        fillColor: [52, 73, 94],
        textColor: [255, 255, 255],
        fontSize: 8,
        fontStyle: 'bold',
        halign: 'center'
      },
      bodyStyles: {
        fontSize: 8,
        cellPadding: 2
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 25 },
        1: { halign: 'left', cellWidth: 50 },
        2: { halign: 'right', cellWidth: 30 },
        3: { halign: 'right', cellWidth: 25 },
        4: { halign: 'right', cellWidth: 25 },
        5: { halign: 'right', cellWidth: 28, fontStyle: 'bold', textColor: [39, 174, 96] }
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250]
      },
      margin: { left: this.margin, right: this.margin }
    });

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15;
  }



  private addFooterSection(data: InvoiceData): void {
    // Amount in words
    this.doc.setFillColor(...this.lightGray);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 12, 'F');
    
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    
    const wordsAmount = parseFloat(data.invoice.totalAmount || '0');
    const amountInWords = convertToWords(wordsAmount);
    this.doc.text('Amount in Words:', this.margin + 5, this.currentY + 5);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(amountInWords, this.margin + 5, this.currentY + 9);
    
    this.currentY += 20;
    
    // Footer information in two columns
    const leftColWidth = (this.pageWidth - 3 * this.margin) / 2;
    
    // Bank details
    if (data.company?.bankDetails) {
      this.doc.setFillColor(255, 255, 255);
      this.doc.setDrawColor(...this.mediumGray);
      this.doc.setLineWidth(0.5);
      this.doc.rect(this.margin, this.currentY, leftColWidth, 30, 'FD');
      
      this.doc.setTextColor(...this.primaryColor);
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text('BANK DETAILS', this.margin + 3, this.currentY + 8);
      
      this.doc.setTextColor(...this.darkColor);
      this.doc.setFontSize(8);
      this.doc.setFont('helvetica', 'normal');
      
      let bankY = this.currentY + 14;
      this.doc.text(`Bank: ${data.company.bankDetails.bankName}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`Account: ${data.company.bankDetails.accountNumber}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`IFSC: ${data.company.bankDetails.ifscCode}`, this.margin + 3, bankY);
      bankY += 4;
      this.doc.text(`Branch: ${data.company.bankDetails.branch}`, this.margin + 3, bankY);
    }
    
    // Terms and signature
    const rightColX = this.margin + leftColWidth + this.margin;
    this.doc.setFillColor(255, 255, 255);
    this.doc.setDrawColor(...this.mediumGray);
    this.doc.setLineWidth(0.5);
    this.doc.rect(rightColX, this.currentY, leftColWidth, 30, 'FD');
    
    this.doc.setTextColor(...this.primaryColor);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('TERMS & CONDITIONS', rightColX + 3, this.currentY + 8);
    
    this.doc.setTextColor(...this.darkColor);
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    
    let termsY = this.currentY + 14;
    this.doc.text('• Payment due within 30 days', rightColX + 3, termsY);
    termsY += 4;
    this.doc.text('• Goods once sold will not be taken back', rightColX + 3, termsY);
    termsY += 4;
    this.doc.text('• Subject to local jurisdiction', rightColX + 3, termsY);
    
    // Signature
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Authorized Signatory', rightColX + leftColWidth - 3, this.currentY + 26, { align: 'right' });
    
    // Footer line
    this.currentY = this.pageHeight - 20;
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(2);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    
    // Thank you message
    this.doc.setTextColor(...this.primaryColor);
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Thank you for your business!', this.pageWidth/2, this.currentY + 8, { align: 'center' });
    
    // Contact info
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`${data.company?.phone || ''} | ${data.company?.email || ''} | ${data.company?.website || ''}`, 
                  this.pageWidth/2, this.currentY + 12, { align: 'center' });
  }

  save(filename: string = 'professional-jewelry-bill.pdf'): void {
    this.doc.save(filename);
  }

  print(): void {
    if (typeof window !== 'undefined') {
      window.open(this.doc.output('bloburl'), '_blank');
    } else {
      console.log('PDF preview is not available in this environment.');
    }
  }
}

// Export functions
export function generateProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config']): void {
  const generator = new ProfessionalBillGenerator(config);
  generator.generateProfessionalBill(data);
  generator.save(`Professional-Bill-${data.invoice.invoiceNumber}.pdf`);
}

export function printProfessionalBill(data: InvoiceData, config?: ProfessionalBillGenerator['config']): void {
  const generator = new ProfessionalBillGenerator(config);
  generator.generateProfessionalBill(data);
  generator.print();
}