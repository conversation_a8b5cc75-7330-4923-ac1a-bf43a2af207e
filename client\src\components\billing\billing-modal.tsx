import { useState, useEffect } from "react";
import { X, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchCustomers, fetchInventoryItems, createInvoice, fetchMetalRates } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { calculateJewelryValues, calculateInvoiceTotals, formatCurrency } from "../../../../shared/jewelry-calculations";
import { PURITY_REFERENCE } from "@/lib/jewelry-calculations";

interface BillingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function BillingModal({ isOpen, onClose }: BillingModalProps) {
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [invoiceItems, setInvoiceItems] = useState<any[]>([]);
  const [subtotal, setSubtotal] = useState(0);
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false);
  const [selectedInventoryItem, setSelectedInventoryItem] = useState<any>(null);
  const [paymentTerms, setPaymentTerms] = useState("net30");
  const [notes, setNotes] = useState("");
  
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
    queryFn: fetchCustomers,
  });

  const { data: inventoryItems } = useQuery({
    queryKey: ["/api/inventory"],
    queryFn: fetchInventoryItems,
  });

  const { data: metalRates } = useQuery({
    queryKey: ["/api/metal-rates/current"],
    queryFn: fetchMetalRates,
  });

  const createInvoiceMutation = useMutation({
    mutationFn: createInvoice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
      resetForm();
      onClose();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create invoice",
        variant: "destructive",
      });
    },
  });

  // Auto-populate rate when inventory item is selected
  useEffect(() => {
    if (selectedInventoryItem && metalRates) {
      // Find the pure metal rate (999 purity for all metals)
      const pureRate = metalRates.find((rate: any) =>
        rate.metalType.toLowerCase() === selectedInventoryItem.metalType.toLowerCase() &&
        rate.purity === "999"
      );

      if (pureRate) {
        const rateValue = parseFloat(pureRate.ratePerGram);
        if (rateValue > 0) {
          // Auto-populate the rate input field
          const rateInput = document.getElementById("ratePerGram") as HTMLInputElement;
          if (rateInput) {
            rateInput.value = rateValue.toString();
          }
        }
      }
    }
  }, [selectedInventoryItem, metalRates]);

  if (!isOpen) return null;

  const resetForm = () => {
    setSelectedCustomer(null);
    setInvoiceItems([]);
    setSubtotal(0);
    setPaymentTerms("net30");
    setNotes("");
    setSelectedInventoryItem(null);
  };



  const addItemToInvoice = (item: any, quantity: number, ratePerGram: number) => {
    // Use the comprehensive jewelry calculation engine
    const calculationInput = {
      grossWeight: parseFloat(item.grossWeight),
      stoneWeight: parseFloat(item.stoneWeight || 0),
      purityPercentage: parseFloat(item.purityPercentage || 96),
      goldRatePerGram: ratePerGram,
      wastagePercentage: parseFloat(item.wastagePercentage || 0),
      makingChargesRate: parseFloat(item.makingChargesRate || 0),
      stoneAmount: parseFloat(item.stoneAmount || 0),
    };

    const calculations = calculateJewelryValues(calculationInput);

    const newItem = {
      ...item,
      quantity,
      ratePerGram,
      // Add all calculated values
      fineWeight: calculations.fineWeight,
      goldValue: calculations.goldValue,
      makingCharges: calculations.makingCharges,
      itemTotal: calculations.itemTotal,
      taxableAmount: calculations.taxableAmount,
      cgstAmount: calculations.cgstAmount,
      sgstAmount: calculations.sgstAmount,
      totalAmount: calculations.grandTotal,
      breakdown: calculations.breakdown
    };

    const newItems = [...invoiceItems, newItem];
    setInvoiceItems(newItems);

    // Calculate invoice totals using the calculation engine
    const invoiceTotals = calculateInvoiceTotals(newItems.map(item => ({
      netWeight: item.netWeight || 0,
      fineWeight: item.fineWeight || 0,
      goldValue: item.goldValue || 0,
      makingCharges: item.makingCharges || 0,
      itemTotal: item.itemTotal || 0,
      taxableAmount: item.taxableAmount || 0,
      cgstAmount: item.cgstAmount || 0,
      sgstAmount: item.sgstAmount || 0,
      grandTotal: item.totalAmount || 0,
      breakdown: item.breakdown || {}
    })));

    setSubtotal(invoiceTotals.totalTaxableAmount);

    setIsAddItemDialogOpen(false);
    setSelectedInventoryItem(null);
  };

  const removeItemFromInvoice = (index: number) => {
    const newItems = invoiceItems.filter((_, i) => i !== index);
    setInvoiceItems(newItems);

    if (newItems.length > 0) {
      // Recalculate invoice totals
      const invoiceTotals = calculateInvoiceTotals(newItems.map(item => ({
        netWeight: item.netWeight || 0,
        fineWeight: item.fineWeight || 0,
        goldValue: item.goldValue || 0,
        makingCharges: item.makingCharges || 0,
        itemTotal: item.itemTotal || 0,
        taxableAmount: item.taxableAmount || 0,
        cgstAmount: item.cgstAmount || 0,
        sgstAmount: item.sgstAmount || 0,
        grandTotal: item.totalAmount || 0,
        breakdown: item.breakdown || {}
      })));

      setSubtotal(invoiceTotals.totalTaxableAmount);
    } else {
      setSubtotal(0);
    }
  };

  const generateInvoiceNumber = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const time = String(Date.now()).slice(-6); // Last 6 digits of timestamp for uniqueness
    return `INV-${year}${month}${day}-${time}`;
  };

  const handleCreateInvoice = (status: 'pending' | 'draft') => {
    if (!selectedCustomer || invoiceItems.length === 0) {
      toast({
        title: "Error",
        description: "Please select a customer and add at least one item",
        variant: "destructive",
      });
      return;
    }

    const invoiceData = {
      invoiceNumber: generateInvoiceNumber(),
      customerId: selectedCustomer.id,
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      subtotal: subtotal.toString(),
      cgstAmount: cgst.toString(),
      sgstAmount: sgst.toString(),
      totalAmount: total.toString(),
      balanceAmount: total.toString(),
      paymentTerms,
      notes,
      status,
      items: invoiceItems.map(item => ({
        itemId: item.id,
        quantity: item.quantity || 1,
        grossWeight: (item.grossWeight || 0).toString(),
        netWeight: (item.netWeight || 0).toString(),
        fineWeight: (item.fineWeight || 0).toString(),
        purityPercentage: (item.purityPercentage || 96).toString(),
        stoneWeight: (item.stoneWeight || 0).toString(),
        stoneAmount: (item.stoneAmount || 0).toString(),
        wastagePercentage: (item.wastagePercentage || 0).toString(),
        ratePerGram: (item.ratePerGram || 0).toString(),
        goldValue: (item.goldValue || 0).toString(),
        makingChargesRate: (item.makingChargesRate || 0).toString(),
        makingCharges: (item.makingCharges || 0).toString(),
        itemTotal: (item.itemTotal || 0).toString(),
        taxableAmount: (item.taxableAmount || 0).toString(),
        cgstAmount: (item.cgstAmount || 0).toString(),
        sgstAmount: (item.sgstAmount || 0).toString(),
        totalAmount: (item.totalAmount || 0).toString(),
      }))
    };

    createInvoiceMutation.mutate(invoiceData);
  };

  const calculateTotals = () => {
    if (invoiceItems.length === 0) {
      return { cgst: 0, sgst: 0, total: 0 };
    }

    // Use the calculation engine for accurate totals
    const invoiceTotals = calculateInvoiceTotals(invoiceItems.map(item => ({
      netWeight: item.netWeight || 0,
      fineWeight: item.fineWeight || 0,
      goldValue: item.goldValue || 0,
      makingCharges: item.makingCharges || 0,
      itemTotal: item.itemTotal || 0,
      taxableAmount: item.taxableAmount || 0,
      cgstAmount: item.cgstAmount || 0,
      sgstAmount: item.sgstAmount || 0,
      grandTotal: item.totalAmount || 0,
      breakdown: item.breakdown || {}
    })));

    return {
      cgst: invoiceTotals.totalCgstAmount,
      sgst: invoiceTotals.totalSgstAmount,
      total: invoiceTotals.grandTotal
    };
  };

  const { cgst, sgst, total } = calculateTotals();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b border-neutral-200 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-neutral-800">New Invoice</h2>
            <button onClick={onClose} className="text-neutral-500 hover:text-neutral-700">
              <X className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Customer Selection */}
              <div>
                <Label htmlFor="customer" className="text-sm font-medium text-neutral-700 mb-2">Customer</Label>
                <Select onValueChange={(value) => {
                  const customer = customers?.find((c: any) => c.id.toString() === value);
                  setSelectedCustomer(customer);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Search customer..." />
                  </SelectTrigger>
                  <SelectContent>
                    {customers?.map((customer: any) => (
                      <SelectItem key={customer.id} value={customer.id.toString()}>
                        {customer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedCustomer && (
                  <div className="mt-2 p-3 bg-neutral-50 rounded-lg">
                    <p className="font-medium text-neutral-800">{selectedCustomer.name}</p>
                    <p className="text-sm text-neutral-600">GSTIN: {selectedCustomer.gstin} • Credit Limit: ₹{selectedCustomer.creditLimit}</p>
                    <p className="text-sm text-neutral-600">Outstanding: ₹{selectedCustomer.outstandingAmount}</p>
                  </div>
                )}
              </div>

              {/* Invoice Details */}
              <div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="invoiceNo" className="text-sm font-medium text-neutral-700 mb-2">Invoice No.</Label>
                    <Input id="invoiceNo" value="INV-2024-1252" readOnly className="bg-neutral-50" />
                  </div>
                  <div>
                    <Label htmlFor="date" className="text-sm font-medium text-neutral-700 mb-2">Date</Label>
                    <Input id="date" type="date" defaultValue={new Date().toISOString().split('T')[0]} />
                  </div>
                </div>
              </div>
            </div>

            {/* Item Selection */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-800">Items</h3>
                <Button onClick={() => setIsAddItemDialogOpen(true)} className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                  <Plus className="w-4 h-4 mr-2" />Add Item
                </Button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border border-neutral-200 rounded-lg">
                  <thead className="bg-neutral-50">
                    <tr>
                      <th className="text-left p-3 text-sm font-semibold text-neutral-700">Item</th>
                      <th className="text-left p-3 text-sm font-semibold text-neutral-700">Purity</th>
                      <th className="text-right p-3 text-sm font-semibold text-neutral-700">Gross Wt (g)</th>
                      <th className="text-right p-3 text-sm font-semibold text-neutral-700">Net Wt (g)</th>
                      <th className="text-right p-3 text-sm font-semibold text-neutral-700">Rate/g</th>
                      <th className="text-right p-3 text-sm font-semibold text-neutral-700">Making</th>
                      <th className="text-right p-3 text-sm font-semibold text-neutral-700">Total</th>
                      <th className="w-10 p-3"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoiceItems.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="p-6 text-center text-neutral-500">
                          No items added yet. Click "Add Item" to start.
                        </td>
                      </tr>
                    ) : (
                      invoiceItems.map((item, index) => (
                        <tr key={index} className="border-t border-neutral-200">
                          <td className="p-3">
                            <div>
                              <p className="font-medium text-neutral-800">{item.itemName}</p>
                              <p className="text-sm text-neutral-600">HSN: {item.hsnCode}</p>
                            </div>
                          </td>
                          <td className="p-3 text-neutral-700">{item.purity}</td>
                          <td className="p-3 text-right text-neutral-700">{item.grossWeight}</td>
                          <td className="p-3 text-right text-neutral-700">{item.netWeight}</td>
                          <td className="p-3 text-right text-neutral-700">₹{item.ratePerGram}</td>
                          <td className="p-3 text-right text-neutral-700">₹{item.makingCharges}</td>
                          <td className="p-3 text-right font-semibold text-neutral-800">₹{item.totalAmount}</td>
                          <td className="p-3">
                            <button 
                              onClick={() => removeItemFromInvoice(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Invoice Summary */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                {/* Payment Terms */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="paymentTerms" className="text-sm font-medium text-neutral-700 mb-2">Payment Terms</Label>
                    <Select value={paymentTerms} onValueChange={setPaymentTerms}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment terms" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="net30">Net 30 Days</SelectItem>
                        <SelectItem value="net15">Net 15 Days</SelectItem>
                        <SelectItem value="cod">Cash on Delivery</SelectItem>
                        <SelectItem value="advance">Advance Payment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="notes" className="text-sm font-medium text-neutral-700 mb-2">Notes</Label>
                    <Textarea 
                      id="notes" 
                      rows={3} 
                      placeholder="Additional notes..." 
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div>
                {/* Invoice Totals */}
                <div className="bg-neutral-50 rounded-lg p-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-600">Subtotal:</span>
                    <span className="text-neutral-800">₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-600">CGST (1.5%):</span>
                    <span className="text-neutral-800">₹{cgst.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-600">SGST (1.5%):</span>
                    <span className="text-neutral-800">₹{sgst.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-600">Discount:</span>
                    <span className="text-neutral-800">₹0</span>
                  </div>
                  <hr className="my-2 border-neutral-300" />
                  <div className="flex justify-between font-semibold text-lg">
                    <span className="text-neutral-800">Total Amount:</span>
                    <span className="text-neutral-900">₹{total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="mt-4 flex space-x-3">
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => handleCreateInvoice('draft')}
                    disabled={createInvoiceMutation.isPending}
                  >
                    {createInvoiceMutation.isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button 
                    className="flex-1 bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
                    onClick={() => handleCreateInvoice('pending')}
                    disabled={createInvoiceMutation.isPending}
                  >
                    {createInvoiceMutation.isPending ? "Creating..." : "Generate Invoice"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Item Dialog */}
      <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Item to Invoice</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="inventoryItem">Select Item</Label>
              <Select onValueChange={(value) => {
                const item = inventoryItems?.find((item: any) => item.id.toString() === value);
                setSelectedInventoryItem(item);
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose inventory item..." />
                </SelectTrigger>
                <SelectContent>
                  {inventoryItems?.map((item: any) => (
                    <SelectItem key={item.id} value={item.id.toString()}>
                      {item.itemName} - {item.itemCode}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedInventoryItem && (
              <div className="bg-neutral-50 p-4 rounded-lg space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Gross Weight:</span> {selectedInventoryItem.grossWeight}g
                  </div>
                  <div>
                    <span className="font-medium">Net Weight:</span> {selectedInventoryItem.netWeight}g
                  </div>
                  <div>
                    <span className="font-medium">Purity:</span> {selectedInventoryItem.purity}
                  </div>
                  <div>
                    <span className="font-medium">Making Charges:</span> ₹{selectedInventoryItem.makingCharges}
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quantity">Quantity</Label>
                <Input id="quantity" type="number" defaultValue="1" min="1" />
              </div>
              <div>
                <Label htmlFor="ratePerGram">
                  Rate per Gram (₹)
                  {selectedInventoryItem && (
                    <span className="text-xs text-green-600 ml-2">
                      (Auto-populated from {selectedInventoryItem.metalType} 24K/999 rate)
                    </span>
                  )}
                </Label>
                <Input
                  id="ratePerGram"
                  type="number"
                  step="0.01"
                  placeholder="Enter current rate"
                  className={selectedInventoryItem ? "border-green-300 bg-green-50" : ""}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddItemDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={() => {
                  if (selectedInventoryItem) {
                    const quantity = parseInt((document.getElementById("quantity") as HTMLInputElement)?.value || "1");
                    const ratePerGram = parseFloat((document.getElementById("ratePerGram") as HTMLInputElement)?.value || "0");
                    addItemToInvoice(selectedInventoryItem, quantity, ratePerGram);
                  }
                }}
                disabled={!selectedInventoryItem}
                className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
              >
                Add to Invoice
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
