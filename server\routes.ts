import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { db } from "./db";
import { insertSupplierSchema, insertCustomerSchema, insertMetalProcurementSchema, insertMetalRateSchema, insertInventoryItemSchema, insertInvoiceSchema, insertInvoiceItemSchema, insertPaymentSchema, invoices, customers, inventoryItems, metalRates, users } from "@shared/schema";
import { and, gte, lte, sql, eq } from "drizzle-orm";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key-change-in-production";

// Middleware for authentication
const authenticateToken = (req: any, res: any, next: any) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Middleware for role-based authorization
const requireRole = (roles: string[]) => {
  return (req: any, res: any, next: any) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Insufficient permissions' });
    }

    next();
  };
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }

      // Find user by username
      const [user] = await db.select().from(users).where(eq(users.username, username));

      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          id: user.id,
          username: user.username,
          role: user.role
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // Return user data (without password) and token
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        token,
        user: userWithoutPassword
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/auth/verify", authenticateToken, async (req, res) => {
    try {
      // Get fresh user data
      const [user] = await db.select().from(users).where(eq(users.id, req.user!.id));

      if (!user) {
        return res.status(401).json({ message: "User not found" });
      }

      const { password: _, ...userWithoutPassword } = user;
      res.json({ user: userWithoutPassword });
    } catch (error) {
      console.error("Token verification error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/auth/logout", authenticateToken, (req, res) => {
    // In a more sophisticated setup, you might want to blacklist the token
    res.json({ message: "Logged out successfully" });
  });

  // Dashboard routes
  app.get("/api/dashboard/stats", authenticateToken, async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ message: "Failed to fetch dashboard stats" });
    }
  });

  // Supplier routes
  app.get("/api/suppliers", async (req, res) => {
    try {
      const suppliers = await storage.getSuppliers();
      res.json(suppliers);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      res.status(500).json({ message: "Failed to fetch suppliers" });
    }
  });

  app.post("/api/suppliers", async (req, res) => {
    try {
      const supplier = insertSupplierSchema.parse(req.body);
      const newSupplier = await storage.createSupplier(supplier);
      res.status(201).json(newSupplier);
    } catch (error) {
      console.error("Error creating supplier:", error);
      res.status(400).json({ message: "Failed to create supplier" });
    }
  });

  // Customer routes
  app.get("/api/customers", async (req, res) => {
    try {
      const customers = await storage.getCustomers();
      res.json(customers);
    } catch (error) {
      console.error("Error fetching customers:", error);
      res.status(500).json({ message: "Failed to fetch customers" });
    }
  });

  app.post("/api/customers", async (req, res) => {
    try {
      const customer = insertCustomerSchema.parse(req.body);
      const newCustomer = await storage.createCustomer(customer);
      res.status(201).json(newCustomer);
    } catch (error) {
      console.error("Error creating customer:", error);
      res.status(400).json({ message: "Failed to create customer" });
    }
  });

  app.put("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const customer = insertCustomerSchema.partial().parse(req.body);
      const updated = await storage.updateCustomer(id, customer);
      if (!updated) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating customer:", error);
      res.status(400).json({ message: "Failed to update customer" });
    }
  });

  app.delete("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteCustomer(id);
      if (!deleted) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting customer:", error);
      res.status(500).json({ message: "Failed to delete customer" });
    }
  });

  app.patch("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const updatedCustomer = await storage.updateCustomer(id, updates);
      if (!updatedCustomer) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.json(updatedCustomer);
    } catch (error) {
      console.error("Error updating customer:", error);
      res.status(400).json({ message: "Failed to update customer" });
    }
  });

  // Metal procurement routes
  app.get("/api/metal-procurement", async (req, res) => {
    try {
      const procurements = await storage.getMetalProcurements();
      res.json(procurements);
    } catch (error) {
      console.error("Error fetching metal procurements:", error);
      res.status(500).json({ message: "Failed to fetch metal procurements" });
    }
  });

  app.post("/api/metal-procurement", async (req, res) => {
    try {
      const procurement = insertMetalProcurementSchema.parse(req.body);
      const newProcurement = await storage.createMetalProcurement(procurement);
      res.status(201).json(newProcurement);
    } catch (error) {
      console.error("Error creating metal procurement:", error);
      res.status(400).json({ message: "Failed to create metal procurement" });
    }
  });

  app.put("/api/metal-procurement/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const procurement = insertMetalProcurementSchema.partial().parse(req.body);
      const updated = await storage.updateMetalProcurement(id, procurement);
      if (!updated) {
        return res.status(404).json({ message: "Metal procurement not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating metal procurement:", error);
      res.status(400).json({ message: "Failed to update metal procurement" });
    }
  });

  app.delete("/api/metal-procurement/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteMetalProcurement(id);
      if (!deleted) {
        return res.status(404).json({ message: "Metal procurement not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting metal procurement:", error);
      res.status(500).json({ message: "Failed to delete metal procurement" });
    }
  });

  // Metal rates routes
  app.get("/api/metal-rates", async (req, res) => {
    try {
      const rates = await storage.getMetalRates();
      res.json(rates);
    } catch (error) {
      console.error("Error fetching metal rates:", error);
      res.status(500).json({ message: "Failed to fetch metal rates" });
    }
  });

  app.get("/api/metal-rates/current", async (req, res) => {
    try {
      const rates = await storage.getCurrentMetalRates();
      res.json(rates);
    } catch (error) {
      console.error("Error fetching current metal rates:", error);
      res.status(500).json({ message: "Failed to fetch current metal rates" });
    }
  });

  app.post("/api/metal-rates", async (req, res) => {
    try {
      console.log("Received metal rate data:", req.body);
      const rate = insertMetalRateSchema.parse(req.body);
      console.log("Parsed metal rate data:", rate);
      const newRate = await storage.createMetalRate(rate);
      console.log("Created metal rate:", newRate);
      res.status(201).json(newRate);
    } catch (error) {
      console.error("Error creating metal rate:", error);
      if (error && typeof error === 'object' && 'issues' in error) {
        console.error("Validation errors:", (error as any).issues);
      }
      res.status(400).json({
        message: "Failed to create metal rate",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.put("/api/metal-rates/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const rate = insertMetalRateSchema.partial().parse(req.body);
      const updated = await storage.updateMetalRate(id, rate);
      if (!updated) {
        return res.status(404).json({ message: "Metal rate not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating metal rate:", error);
      res.status(400).json({ message: "Failed to update metal rate" });
    }
  });

  app.delete("/api/metal-rates/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteMetalRate(id);
      if (!deleted) {
        return res.status(404).json({ message: "Metal rate not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting metal rate:", error);
      res.status(500).json({ message: "Failed to delete metal rate" });
    }
  });

  // Inventory routes
  app.get("/api/inventory", async (req, res) => {
    try {
      const items = await storage.getInventoryItems();
      res.json(items);
    } catch (error) {
      console.error("Error fetching inventory items:", error);
      res.status(500).json({ message: "Failed to fetch inventory items" });
    }
  });

  app.get("/api/inventory/next-code", async (req, res) => {
    try {
      const metalType = req.query.metalType as string || 'gold';
      const prefix = req.query.prefix as string;
      const nextCode = await storage.generateUniqueItemCode(metalType, prefix);
      res.json({ itemCode: nextCode });
    } catch (error) {
      console.error("Error generating item code:", error);
      res.status(500).json({ message: "Failed to generate item code" });
    }
  });

  app.post("/api/inventory", async (req, res) => {
    try {
      // Validate request body
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({
          success: false,
          message: "Invalid request body",
          error: "Request body must be a valid JSON object"
        });
      }

      // Parse and validate with Zod schema
      const item = insertInventoryItemSchema.parse(req.body);

      // Create the inventory item
      const newItem = await storage.createInventoryItem(item);

      // Return success response
      res.status(201).json({
        success: true,
        message: "Inventory item created successfully",
        data: newItem
      });

    } catch (error) {
      console.error("Error creating inventory item:", error);

      // Handle Zod validation errors
      if (error && typeof error === 'object' && 'issues' in error) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          error: "Invalid input data",
          details: error.issues
        });
      }

      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      res.status(500).json({
        success: false,
        message: "Failed to create inventory item",
        error: errorMessage
      });
    }
  });

  app.put("/api/inventory/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid inventory item ID",
          error: "ID must be a valid number"
        });
      }

      // Validate request body
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({
          success: false,
          message: "Invalid request body",
          error: "Request body must be a valid JSON object"
        });
      }

      // Parse and validate with Zod schema
      const item = insertInventoryItemSchema.partial().parse(req.body);

      // Update the inventory item
      const updated = await storage.updateInventoryItem(id, item);

      if (!updated) {
        return res.status(404).json({
          success: false,
          message: "Inventory item not found",
          error: `No inventory item found with ID ${id}`
        });
      }

      res.json({
        success: true,
        message: "Inventory item updated successfully",
        data: updated
      });

    } catch (error) {
      console.error("Error updating inventory item:", error);

      // Handle Zod validation errors
      if (error && typeof error === 'object' && 'issues' in error) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          error: "Invalid input data",
          details: error.issues
        });
      }

      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      res.status(500).json({
        success: false,
        message: "Failed to update inventory item",
        error: errorMessage
      });
    }
  });

  app.delete("/api/inventory/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid inventory item ID",
          error: "ID must be a valid number"
        });
      }

      // Delete the inventory item (soft delete)
      const deleted = await storage.deleteInventoryItem(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          message: "Inventory item not found or already deleted",
          error: `No active inventory item found with ID ${id}. The item may have already been deleted.`
        });
      }

      res.json({
        success: true,
        message: "Inventory item deleted successfully",
        data: { id }
      });

    } catch (error) {
      console.error("Error deleting inventory item:", error);

      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      res.status(500).json({
        success: false,
        message: "Failed to delete inventory item",
        error: errorMessage
      });
    }
  });

  app.patch("/api/inventory/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const updatedItem = await storage.updateInventoryItem(id, updates);
      if (!updatedItem) {
        return res.status(404).json({ message: "Inventory item not found" });
      }
      res.json(updatedItem);
    } catch (error) {
      console.error("Error updating inventory item:", error);
      res.status(400).json({ message: "Failed to update inventory item" });
    }
  });

  // Invoice routes
  app.get("/api/invoices", async (req, res) => {
    try {
      const invoices = await storage.getInvoices();
      res.json(invoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ message: "Failed to fetch invoices" });
    }
  });

  app.get("/api/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getInvoice(id);
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      // Also get the invoice items and customer details
      const items = await storage.getInvoiceItems(id);
      const customers = await storage.getCustomers();
      const customer = customers.find(c => c.id === invoice.customerId);

      res.json({
        ...invoice,
        items,
        customer
      });
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ message: "Failed to fetch invoice" });
    }
  });

  app.post("/api/invoices", async (req, res) => {
    try {
      const { items, ...invoiceData } = req.body;
      
      // Validate invoice data
      const invoice = insertInvoiceSchema.parse(invoiceData);
      
      // Create the invoice
      const newInvoice = await storage.createInvoice(invoice);
      
      // Create invoice items if provided
      if (items && Array.isArray(items) && items.length > 0) {
        for (const item of items) {
          const invoiceItem = {
            invoiceId: newInvoice.id,
            ...item
          };
          await storage.createInvoiceItem(invoiceItem);
        }
      }
      
      res.status(201).json(newInvoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(400).json({ message: "Failed to create invoice" });
    }
  });

  app.put("/api/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = insertInvoiceSchema.partial().parse(req.body);
      const updated = await storage.updateInvoice(id, invoice);
      if (!updated) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating invoice:", error);
      res.status(400).json({ message: "Failed to update invoice" });
    }
  });

  app.delete("/api/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteInvoice(id);
      if (!deleted) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting invoice:", error);
      res.status(500).json({ message: "Failed to delete invoice" });
    }
  });

  app.get("/api/invoices/:id/items", async (req, res) => {
    try {
      const invoiceId = parseInt(req.params.id);
      const items = await storage.getInvoiceItems(invoiceId);
      res.json(items);
    } catch (error) {
      console.error("Error fetching invoice items:", error);
      res.status(500).json({ message: "Failed to fetch invoice items" });
    }
  });

  app.get("/api/invoices/:id/pdf", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getInvoice(id);
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      // Get related data
      const items = await storage.getInvoiceItems(id);
      const customers = await storage.getCustomers();
      const customer = customers.find(c => c.id === invoice.customerId);

      // For now, return JSON data that can be used to generate PDF on frontend
      // In a production app, you'd use a library like puppeteer or jsPDF here
      // Get company settings from database
      const companySettings = await storage.getSettings();
      const getSettingValue = (key: string, defaultValue: string, category?: string) => {
        const setting = companySettings.find(s =>
          s.key === key && (!category || s.category === category)
        );
        return setting?.value || defaultValue;
      };

      res.json({
        invoice,
        items,
        customer,
        company: {
          name: getSettingValue("name", "JewelPro", "company"),
          address: getSettingValue("address", "123 Jewelry Street, Chennai, Tamil Nadu", "company"),
          phone: getSettingValue("phone", "+91-9876543210", "company"),
          email: getSettingValue("email", "<EMAIL>", "company"),
          gstNumber: getSettingValue("gst", "33ABCDE1234F5Z6", "company"),
          website: getSettingValue("website", "www.jewelpro.com", "company")
        }
      });
    } catch (error) {
      console.error("Error generating invoice PDF:", error);
      res.status(500).json({ message: "Failed to generate invoice PDF" });
    }
  });

  app.post("/api/invoice-items", async (req, res) => {
    try {
      const item = insertInvoiceItemSchema.parse(req.body);
      const newItem = await storage.createInvoiceItem(item);
      res.status(201).json(newItem);
    } catch (error) {
      console.error("Error creating invoice item:", error);
      res.status(400).json({ message: "Failed to create invoice item" });
    }
  });

  // Payment routes
  app.get("/api/payments", async (req, res) => {
    try {
      const payments = await storage.getPayments();
      res.json(payments);
    } catch (error) {
      console.error("Error fetching payments:", error);
      res.status(500).json({ message: "Failed to fetch payments" });
    }
  });

  app.get("/api/customers/:id/payments", async (req, res) => {
    try {
      const customerId = parseInt(req.params.id);
      const payments = await storage.getCustomerPayments(customerId);
      res.json(payments);
    } catch (error) {
      console.error("Error fetching customer payments:", error);
      res.status(500).json({ message: "Failed to fetch customer payments" });
    }
  });

  app.post("/api/payments", async (req, res) => {
    try {
      const payment = insertPaymentSchema.parse(req.body);
      const newPayment = await storage.createPayment(payment);
      res.status(201).json(newPayment);
    } catch (error) {
      console.error("Error creating payment:", error);
      res.status(400).json({ message: "Failed to create payment" });
    }
  });

  app.put("/api/payments/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const payment = insertPaymentSchema.partial().parse(req.body);
      const updated = await storage.updatePayment(id, payment);
      if (!updated) {
        return res.status(404).json({ message: "Payment not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating payment:", error);
      res.status(400).json({ message: "Failed to update payment" });
    }
  });

  app.delete("/api/payments/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deletePayment(id);
      if (!deleted) {
        return res.status(404).json({ message: "Payment not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting payment:", error);
      res.status(500).json({ message: "Failed to delete payment" });
    }
  });

  // Supplier routes
  app.get("/api/suppliers", async (req, res) => {
    try {
      const suppliers = await storage.getSuppliers();
      res.json(suppliers);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      res.status(500).json({ message: "Failed to fetch suppliers" });
    }
  });

  app.post("/api/suppliers", async (req, res) => {
    try {
      const supplier = insertSupplierSchema.parse(req.body);
      const newSupplier = await storage.createSupplier(supplier);
      res.status(201).json(newSupplier);
    } catch (error) {
      console.error("Error creating supplier:", error);
      res.status(400).json({ message: "Failed to create supplier" });
    }
  });

  app.put("/api/suppliers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const supplier = insertSupplierSchema.partial().parse(req.body);
      const updated = await storage.updateSupplier(id, supplier);
      if (!updated) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.json(updated);
    } catch (error) {
      console.error("Error updating supplier:", error);
      res.status(400).json({ message: "Failed to update supplier" });
    }
  });

  app.delete("/api/suppliers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteSupplier(id);
      if (!deleted) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting supplier:", error);
      res.status(500).json({ message: "Failed to delete supplier" });
    }
  });

  // Reports routes
  app.get("/api/reports", async (req, res) => {
    try {
      const { type, dateRange, startDate, endDate } = req.query;

      // Calculate date range
      let fromDate = new Date();
      let toDate = new Date();

      if (dateRange === "custom" && startDate && endDate) {
        fromDate = new Date(startDate as string);
        toDate = new Date(endDate as string);
      } else {
        const days = parseInt(dateRange as string) || 30;
        fromDate.setDate(toDate.getDate() - days);
      }

      const reports: any = {};

      if (type === "sales" || !type) {
        // Sales report data
        const salesData = await db.select({
          date: sql`DATE(${invoices.invoiceDate})`,
          totalSales: sql`SUM(${invoices.totalAmount})`,
          orderCount: sql`COUNT(*)`,
          avgOrderValue: sql`AVG(${invoices.totalAmount})`
        })
        .from(invoices)
        .where(and(
          gte(invoices.invoiceDate, fromDate),
          lte(invoices.invoiceDate, toDate)
        ))
        .groupBy(sql`DATE(${invoices.invoiceDate})`)
        .orderBy(sql`DATE(${invoices.invoiceDate})`);

        reports.sales = salesData;
      }

      if (type === "inventory" || !type) {
        // Inventory report data
        const inventoryData = await db.select({
          metalType: inventoryItems.metalType,
          purity: inventoryItems.purity,
          totalItems: sql`COUNT(*)`,
          totalWeight: sql`SUM(${inventoryItems.grossWeight})`,
          totalValue: sql`SUM(${inventoryItems.grossWeight} * 6000)` // Approximate value
        })
        .from(inventoryItems)
        .where(eq(inventoryItems.isActive, true))
        .groupBy(inventoryItems.metalType, inventoryItems.purity);

        reports.inventory = inventoryData;
      }

      if (type === "customers" || !type) {
        // Customer analysis data
        const customerData = await db.select({
          customerId: invoices.customerId,
          customerName: customers.name,
          totalOrders: sql`COUNT(${invoices.id})`,
          totalValue: sql`SUM(${invoices.totalAmount})`,
          avgOrderValue: sql`AVG(${invoices.totalAmount})`,
          lastOrderDate: sql`MAX(${invoices.invoiceDate})`
        })
        .from(invoices)
        .innerJoin(customers, eq(invoices.customerId, customers.id))
        .where(and(
          gte(invoices.invoiceDate, fromDate),
          lte(invoices.invoiceDate, toDate)
        ))
        .groupBy(invoices.customerId, customers.name)
        .orderBy(sql`SUM(${invoices.totalAmount}) DESC`)
        .limit(10);

        reports.customers = customerData;
      }

      if (type === "financial" || !type) {
        // Financial summary
        const financialData = await db.select({
          totalRevenue: sql`SUM(${invoices.totalAmount})`,
          totalPaid: sql`SUM(${invoices.paidAmount})`,
          totalOutstanding: sql`SUM(${invoices.balanceAmount})`,
          totalInvoices: sql`COUNT(*)`,
          avgInvoiceValue: sql`AVG(${invoices.totalAmount})`
        })
        .from(invoices)
        .where(and(
          gte(invoices.invoiceDate, fromDate),
          lte(invoices.invoiceDate, toDate)
        ));

        reports.financial = financialData[0] || {};
      }

      if (type === "rates" || !type) {
        // Metal rates trend
        const ratesData = await db.select()
        .from(metalRates)
        .where(and(
          gte(metalRates.rateDate, fromDate),
          lte(metalRates.rateDate, toDate)
        ))
        .orderBy(metalRates.rateDate);

        reports.rates = ratesData;
      }

      res.json(reports);
    } catch (error) {
      console.error("Error fetching reports:", error);
      res.status(500).json({ message: "Failed to fetch reports" });
    }
  });

  // Settings routes
  app.get("/api/settings", async (req, res) => {
    try {
      const { category } = req.query;
      const settings = await storage.getSettings(category as string);
      res.json(settings);
    } catch (error) {
      console.error("Error fetching settings:", error);
      res.status(500).json({ message: "Failed to fetch settings" });
    }
  });

  app.get("/api/settings/:category/:key", async (req, res) => {
    try {
      const { category, key } = req.params;
      const setting = await storage.getSetting(category, key);
      if (!setting) {
        return res.status(404).json({ message: "Setting not found" });
      }
      res.json(setting);
    } catch (error) {
      console.error("Error fetching setting:", error);
      res.status(500).json({ message: "Failed to fetch setting" });
    }
  });

  app.post("/api/settings", async (req, res) => {
    try {
      const setting = await storage.setSetting(req.body);
      res.status(201).json(setting);
    } catch (error) {
      console.error("Error saving setting:", error);
      res.status(500).json({ message: "Failed to save setting" });
    }
  });

  app.put("/api/settings/:category/:key", async (req, res) => {
    try {
      const { category, key } = req.params;
      const settingData = { ...req.body, category, key };
      const setting = await storage.setSetting(settingData);
      res.json(setting);
    } catch (error) {
      console.error("Error updating setting:", error);
      res.status(500).json({ message: "Failed to update setting" });
    }
  });

  app.delete("/api/settings/:category/:key", async (req, res) => {
    try {
      const { category, key } = req.params;
      const success = await storage.deleteSetting(category, key);
      if (!success) {
        return res.status(404).json({ message: "Setting not found" });
      }
      res.json({ message: "Setting deleted successfully" });
    } catch (error) {
      console.error("Error deleting setting:", error);
      res.status(500).json({ message: "Failed to delete setting" });
    }
  });

  // Settings cleanup endpoint (for development/admin use)
  app.post("/api/settings/cleanup", async (req, res) => {
    try {
      await storage.cleanupSettingsValues();
      res.json({ message: "Settings cleanup completed successfully" });
    } catch (error) {
      console.error("Error during settings cleanup:", error);
      res.status(500).json({ message: "Failed to cleanup settings" });
    }
  });

  // Settings seeding endpoint (for development/admin use)
  app.post("/api/settings/seed", async (req, res) => {
    try {
      await storage.seedDefaultSettings();
      res.json({ message: "Default settings seeded successfully" });
    } catch (error) {
      console.error("Error during settings seeding:", error);
      res.status(500).json({ message: "Failed to seed default settings" });
    }
  });

  // Debug endpoint to check metal rates data
  app.get("/api/debug/metal-rates", async (req, res) => {
    try {
      const allRates = await storage.getMetalRates();
      const currentRates = await storage.getCurrentMetalRates();
      res.json({
        allRates: allRates,
        currentRates: currentRates,
        allRatesCount: allRates.length,
        currentRatesCount: currentRates.length
      });
    } catch (error) {
      console.error("Error in debug endpoint:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Fix endpoint to activate all existing rates
  app.post("/api/debug/fix-rates", async (req, res) => {
    try {
      // Update all rates to be active (temporary fix)
      await db.update(metalRates).set({ isActive: true });
      const currentRates = await storage.getCurrentMetalRates();
      res.json({
        message: "All rates activated",
        currentRatesCount: currentRates.length,
        currentRates: currentRates
      });
    } catch (error) {
      console.error("Error fixing rates:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Mock API endpoint for testing custom API integration
  app.get("/api/mock-rates", (req, res) => {
    // Simulate a custom API response with various data structures
    const mockResponse = {
      status: "success",
      data: {
        metals: {
          gold: {
            rate: 7850.50 + (Math.random() * 100 - 50), // Add some variation
            purity: "999",
            unit: "per_gram",
            currency: "INR"
          },
          silver: {
            rate: 95.25 + (Math.random() * 5 - 2.5), // Add some variation
            purity: "999",
            unit: "per_gram",
            currency: "INR"
          },
          platinum: {
            rate: 3200.00 + (Math.random() * 200 - 100), // Add some variation
            purity: "950",
            unit: "per_gram",
            currency: "INR"
          }
        },
        timestamp: new Date().toISOString(),
        source: "Mock API Provider",
        last_updated: new Date().toISOString()
      }
    };

    // Add some delay to simulate real API
    setTimeout(() => {
      res.json(mockResponse);
    }, 500 + Math.random() * 1000); // 0.5-1.5 second delay
  });

  // User management routes (admin only)
  app.get("/api/users", authenticateToken, requireRole(['admin']), async (req, res) => {
    try {
      const allUsers = await db.select({
        id: users.id,
        username: users.username,
        name: users.name,
        role: users.role,
        createdAt: users.createdAt
      }).from(users);

      res.json(allUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.post("/api/users", authenticateToken, requireRole(['admin']), async (req, res) => {
    try {
      const { username, password, name, role } = req.body;

      if (!username || !password || !name || !role) {
        return res.status(400).json({ message: "All fields are required" });
      }

      // Check if username already exists
      const [existingUser] = await db.select().from(users).where(eq(users.username, username));
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const result = await db.insert(users).values({
        username,
        password: hashedPassword,
        name,
        role
      });

      const insertId = Array.isArray(result) ? result[0].insertId : result.insertId;
      const [newUser] = await db.select({
        id: users.id,
        username: users.username,
        name: users.name,
        role: users.role,
        createdAt: users.createdAt
      }).from(users).where(eq(users.id, insertId));

      res.status(201).json(newUser);
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Templates routes
  app.get("/api/templates", authenticateToken, async (req, res) => {
    try {
      // For now, return empty array since we don't have database templates yet
      // This will be replaced with actual database queries once templates table is created
      res.json([]);
    } catch (error) {
      console.error("Error fetching templates:", error);
      res.status(500).json({ message: "Failed to fetch templates" });
    }
  });

  app.get("/api/templates/:id", authenticateToken, async (req, res) => {
    try {
      // For now, return 404 since we don't have database templates yet
      res.status(404).json({ message: "Template not found" });
    } catch (error) {
      console.error("Error fetching template:", error);
      res.status(500).json({ message: "Failed to fetch template" });
    }
  });

  app.post("/api/templates", authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
    try {
      // For now, return success message
      res.status(201).json({ message: "Template creation will be implemented with database" });
    } catch (error) {
      console.error("Error creating template:", error);
      res.status(500).json({ message: "Failed to create template" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
