import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';

export interface Template {
  id: string;
  name: string;
  description?: string;
  category: 'modern' | 'classic' | 'minimal' | 'luxury' | 'custom';
  colors: {
    primary: number[];
    accent: number[];
    dark: number[];
    lightGray: number[];
    mediumGray: number[];
  };
  layout: {
    margin: number;
    headerHeight: number;
    sectionSpacing: number;
    fontSize: {
      header: number;
      subheader: number;
      body: number;
      small: number;
    };
  };
  settings: {
    showLogo: boolean;
    showWatermark: boolean;
    showGSTBreakdown: boolean;
    showBankDetails: boolean;
    showTermsAndConditions: boolean;
    compactMode: boolean;
    currency: string;
    dateFormat: string;
    numberFormat: string;
  };
  customFields?: Record<string, any>;
  isDefault: boolean;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTemplateRequest {
  name: string;
  description?: string;
  category: Template['category'];
  colors: Template['colors'];
  layout: Template['layout'];
  settings: Template['settings'];
  customFields?: Record<string, any>;
}

export interface UpdateTemplateRequest extends Partial<CreateTemplateRequest> {}

export interface TemplateStats {
  total: number;
  active: number;
  custom: number;
  system: number;
}

// Get all templates
export const useTemplates = () => {
  return useQuery({
    queryKey: ['templates'],
    queryFn: async (): Promise<Template[]> => {
      const response = await apiRequest('GET', '/api/templates');
      const data = await response.json();
      return data.map((template: any) => ({
        ...template,
        colors: typeof template.colors === 'string' ? JSON.parse(template.colors) : template.colors,
        layout: typeof template.layout === 'string' ? JSON.parse(template.layout) : template.layout,
        settings: typeof template.settings === 'string' ? JSON.parse(template.settings) : template.settings,
        customFields: typeof template.customFields === 'string' ? JSON.parse(template.customFields) : template.customFields,
      }));
    },
  });
};

// Get template by ID
export const useTemplate = (id: string) => {
  return useQuery({
    queryKey: ['template', id],
    queryFn: async (): Promise<Template> => {
      const response = await apiRequest('GET', `/api/templates/${id}`);
      const template = await response.json();
      return {
        ...template,
        colors: typeof template.colors === 'string' ? JSON.parse(template.colors) : template.colors,
        layout: typeof template.layout === 'string' ? JSON.parse(template.layout) : template.layout,
        settings: typeof template.settings === 'string' ? JSON.parse(template.settings) : template.settings,
        customFields: typeof template.customFields === 'string' ? JSON.parse(template.customFields) : template.customFields,
      };
    },
    enabled: !!id,
  });
};

// Create template
export const useCreateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTemplateRequest): Promise<Template> => {
      const response = await apiRequest('POST', '/api/templates', data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};

// Update template
export const useUpdateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateTemplateRequest }): Promise<Template> => {
      const response = await apiRequest('PUT', `/api/templates/${id}`, data);
      return await response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template', id] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};

// Delete template
export const useDeleteTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await apiRequest('DELETE', `/api/templates/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};

// Clone template
export const useCloneTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, name }: { id: string; name: string }): Promise<Template> => {
      const response = await apiRequest('POST', `/api/templates/${id}/clone`, { name });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};

// Set default template
export const useSetDefaultTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<Template> => {
      const response = await apiRequest('POST', `/api/templates/${id}/set-default`);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};

// Get template statistics
export const useTemplateStats = () => {
  return useQuery({
    queryKey: ['template-stats'],
    queryFn: async (): Promise<TemplateStats> => {
      const response = await apiRequest('GET', '/api/templates/stats/overview');
      return await response.json();
    },
  });
};

// Seed default templates (admin only)
export const useSeedTemplates = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (): Promise<void> => {
      await apiRequest('POST', '/api/templates/seed');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template-stats'] });
    },
  });
};
