import { useState, useEffect } from 'react';
import { templateManager } from '../lib/template-manager';
import { InvoiceTemplate } from '../types/template';

export const useTemplates = () => {
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([]);
  const [defaultTemplate, setDefaultTemplate] = useState<InvoiceTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load templates from template manager (client-side for now)
      const allTemplates = templateManager.getAllTemplates();
      const defaultTpl = templateManager.getDefaultTemplate();
      
      setTemplates(allTemplates);
      setDefaultTemplate(defaultTpl);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (templateData: any) => {
    try {
      const newTemplate = templateManager.createTemplate(templateData);
      await loadTemplates(); // Refresh the list
      return newTemplate;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to create template');
    }
  };

  const updateTemplate = async (id: string, templateData: any) => {
    try {
      const updatedTemplate = templateManager.updateTemplate({ id, ...templateData });
      await loadTemplates(); // Refresh the list
      return updatedTemplate;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to update template');
    }
  };

  const deleteTemplate = async (id: string) => {
    try {
      const success = templateManager.deleteTemplate(id);
      if (success) {
        await loadTemplates(); // Refresh the list
      }
      return success;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to delete template');
    }
  };

  const setAsDefault = async (id: string) => {
    try {
      const success = templateManager.setDefaultTemplate(id);
      if (success) {
        await loadTemplates(); // Refresh the list
      }
      return success;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to set default template');
    }
  };

  const cloneTemplate = async (id: string, newName: string) => {
    try {
      const clonedTemplate = templateManager.cloneTemplate(id, newName);
      if (clonedTemplate) {
        await loadTemplates(); // Refresh the list
      }
      return clonedTemplate;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to clone template');
    }
  };

  const getTemplate = (id: string) => {
    return templateManager.getTemplate(id);
  };

  const searchTemplates = (query: string) => {
    return templateManager.searchTemplates(query);
  };

  const getTemplateStats = () => {
    return templateManager.getTemplateStats();
  };

  return {
    templates,
    defaultTemplate,
    loading,
    error,
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setAsDefault,
    cloneTemplate,
    getTemplate,
    searchTemplates,
    getTemplateStats
  };
};