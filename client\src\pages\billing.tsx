import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search, Eye, Download, Printer } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import BillingModal from "@/components/billing/billing-modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { useToast } from "@/hooks/use-toast";
import { fetchInvoices, fetchInvoice, fetchInvoicePDF } from "@/lib/api";
import { generateProfessionalBill, printProfessionalBill } from "@/lib/professional-bill";
import { prepareInvoiceData } from "@/lib/invoice-data-helper";

export default function Billing() {
  const [isBillingModalOpen, setIsBillingModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const { toast } = useToast();

  const { data: invoices, isLoading } = useQuery({
    queryKey: ["/api/invoices"],
    queryFn: fetchInvoices,
  });

  const filteredInvoices = invoices?.filter((invoice: any) =>
    invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      default:
        return "bg-neutral-100 text-neutral-800";
    }
  };

  const handleViewInvoice = async (invoiceId: number) => {
    try {
      const invoice = await fetchInvoice(invoiceId);
      setSelectedInvoice(invoice);
      setIsViewModalOpen(true);
    } catch (error) {
      console.error("Error fetching invoice:", error);
    }
  };

  const handleDownloadInvoice = async (invoiceId: number) => {
    try {
      const rawPdfData = await fetchInvoicePDF(invoiceId);
      const enhancedPdfData = await prepareInvoiceData(rawPdfData);

      // Generate professional jewelry bill
      generateProfessionalBill(enhancedPdfData);

      toast({
        title: "Professional Bill Generated",
        description: `Professional jewelry bill ${enhancedPdfData.invoice.invoiceNumber} has been downloaded.`,
      });
    } catch (error) {
      console.error("Error downloading invoice:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePrintInvoice = async (invoiceId: number) => {
    try {
      const rawPdfData = await fetchInvoicePDF(invoiceId);
      const enhancedPdfData = await prepareInvoiceData(rawPdfData);

      // Print professional jewelry bill
      printProfessionalBill(enhancedPdfData);

      toast({
        title: "Print Dialog Opened",
        description: "Professional jewelry bill opened for printing.",
      });
    } catch (error) {
      console.error("Error preparing invoice for print:", error);
      toast({
        title: "Error",
        description: "Failed to prepare invoice for printing.",
        variant: "destructive",
      });
    }
  };



  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Billing & Invoices" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
            </div>
            
            <Button 
              onClick={() => setIsBillingModalOpen(true)}
              className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Invoice
            </Button>
          </div>

          {/* Invoices List */}
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-neutral-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-neutral-200 rounded w-1/2 mb-4"></div>
                      <div className="h-3 bg-neutral-200 rounded w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredInvoices.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <h3 className="text-lg font-semibold text-neutral-700 mb-2">No invoices found</h3>
                <p className="text-neutral-500 mb-4">
                  {searchTerm ? "No invoices match your search criteria." : "Create your first invoice to get started."}
                </p>
                <Button 
                  onClick={() => setIsBillingModalOpen(true)}
                  className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Invoice
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredInvoices.map((invoice: any) => (
                <Card key={invoice.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="text-lg font-semibold text-neutral-900">
                            {invoice.invoiceNumber}
                          </h3>
                          <Badge className={getStatusColor(invoice.status)}>
                            {invoice.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <p className="text-neutral-600">Customer ID</p>
                            <p className="font-medium">#{invoice.customerId}</p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Invoice Date</p>
                            <p className="font-medium">
                              {new Date(invoice.invoiceDate).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-neutral-500">
                              Due: {new Date(invoice.dueDate).toLocaleDateString()}
                            </p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Subtotal</p>
                            <p className="font-medium">
                              ₹{parseInt(invoice.subtotal).toLocaleString("en-IN")}
                            </p>
                            <p className="text-xs text-neutral-500">
                              +₹{(parseFloat(invoice.cgstAmount) + parseFloat(invoice.sgstAmount)).toFixed(2)} GST
                            </p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Total Amount</p>
                            <p className="font-semibold text-lg">
                              ₹{parseInt(invoice.totalAmount).toLocaleString("en-IN")}
                            </p>
                            <p className="text-xs text-neutral-500">
                              Paid: ₹{parseInt(invoice.paidAmount).toLocaleString("en-IN")}
                            </p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Balance Due</p>
                            <p className={`font-semibold text-lg ${
                              parseFloat(invoice.balanceAmount) > 0 ? 'text-red-600' : 'text-green-600'
                            }`}>
                              ₹{parseInt(invoice.balanceAmount).toLocaleString("en-IN")}
                            </p>
                            {parseFloat(invoice.balanceAmount) > 0 && (
                              <p className="text-xs text-red-500">Outstanding</p>
                            )}
                          </div>
                        </div>
                        
                        {invoice.notes && (
                          <div className="mt-3">
                            <p className="text-sm text-neutral-600">Notes: {invoice.notes}</p>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewInvoice(invoice.id)}
                          title="View Invoice"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        
                        {/* Download Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownloadInvoice(invoice.id)}
                          title="Download Professional Bill"
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>

                        {/* Print Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePrintInvoice(invoice.id)}
                          title="Print Professional Bill"
                        >
                          <Printer className="w-4 h-4 mr-1" />
                          Print
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>

      <BillingModal
        isOpen={isBillingModalOpen}
        onClose={() => setIsBillingModalOpen(false)}
      />

      {/* Invoice View Modal */}
      {isViewModalOpen && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Invoice {selectedInvoice.invoiceNumber}</h2>
              <Button
                variant="outline"
                onClick={() => setIsViewModalOpen(false)}
              >
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold mb-2">Customer Details</h3>
                  <p><strong>Name:</strong> {selectedInvoice.customer?.name || 'N/A'}</p>
                  <p><strong>Phone:</strong> {selectedInvoice.customer?.phone || 'N/A'}</p>
                  <p><strong>Email:</strong> {selectedInvoice.customer?.email || 'N/A'}</p>
                  <p><strong>GSTIN:</strong> {selectedInvoice.customer?.gstin || 'N/A'}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Invoice Details</h3>
                  <p><strong>Date:</strong> {new Date(selectedInvoice.invoiceDate).toLocaleDateString()}</p>
                  <p><strong>Due Date:</strong> {new Date(selectedInvoice.dueDate).toLocaleDateString()}</p>
                  <div><strong>Status:</strong> <Badge className={getStatusColor(selectedInvoice.status)}>{selectedInvoice.status}</Badge></div>
                  <p><strong>Payment Terms:</strong> {selectedInvoice.paymentTerms}</p>
                </div>
              </div>

              {selectedInvoice.items && selectedInvoice.items.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-2">Items</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 text-sm">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 p-2 text-left">Item</th>
                          <th className="border border-gray-300 p-2 text-center">Qty</th>
                          <th className="border border-gray-300 p-2 text-left">Metal/Purity</th>
                          <th className="border border-gray-300 p-2 text-right">Gross Wt</th>
                          <th className="border border-gray-300 p-2 text-right">Net Wt</th>
                          <th className="border border-gray-300 p-2 text-right">Stone Wt</th>
                          <th className="border border-gray-300 p-2 text-right">Rate/g</th>
                          <th className="border border-gray-300 p-2 text-right">Making</th>
                          <th className="border border-gray-300 p-2 text-right">Stone Amt</th>
                          <th className="border border-gray-300 p-2 text-center">HSN</th>
                          <th className="border border-gray-300 p-2 text-right">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedInvoice.items.map((item: any, index: number) => {
                          // Calculate stone weight (gross - net)
                          const stoneWeight = (parseFloat(item.grossWeight) - parseFloat(item.netWeight)).toFixed(2);

                          return (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border border-gray-300 p-2">
                                <div className="font-medium">{item.itemName || `Item #${item.itemId}`}</div>
                                <div className="text-xs text-gray-500">ID: {item.itemId}</div>
                              </td>
                              <td className="border border-gray-300 p-2 text-center">{item.quantity}</td>
                              <td className="border border-gray-300 p-2">
                                <div className="font-medium">{item.metalType || 'Gold'}</div>
                                <div className="text-xs text-gray-500">{item.purity || '22K'}</div>
                              </td>
                              <td className="border border-gray-300 p-2 text-right">{parseFloat(item.grossWeight).toFixed(2)}g</td>
                              <td className="border border-gray-300 p-2 text-right">{parseFloat(item.netWeight).toFixed(2)}g</td>
                              <td className="border border-gray-300 p-2 text-right">
                                <span className="font-medium text-orange-600">{stoneWeight}g</span>
                              </td>
                              <td className="border border-gray-300 p-2 text-right">₹{parseFloat(item.ratePerGram).toFixed(2)}</td>
                              <td className="border border-gray-300 p-2 text-right">₹{parseFloat(item.makingCharges).toFixed(2)}</td>
                              <td className="border border-gray-300 p-2 text-right">₹{parseFloat(item.stoneCharges || '0').toFixed(2)}</td>
                              <td className="border border-gray-300 p-2 text-center">
                                <span className="bg-gray-100 px-2 py-1 rounded text-xs">{item.hsnCode || '7113'}</span>
                              </td>
                              <td className="border border-gray-300 p-2 text-right font-semibold">₹{parseFloat(item.totalAmount).toFixed(2)}</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Invoice Summary */}
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-4">Invoice Summary</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>₹{parseFloat(selectedInvoice.subtotal).toLocaleString("en-IN")}</span>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>CGST (1.5%):</span>
                        <span>₹{parseFloat(selectedInvoice.cgstAmount).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>SGST (1.5%):</span>
                        <span>₹{parseFloat(selectedInvoice.sgstAmount).toFixed(2)}</span>
                      </div>
                      {parseFloat(selectedInvoice.discountAmount) > 0 && (
                        <div className="flex justify-between text-sm text-green-600">
                          <span>Discount:</span>
                          <span>-₹{parseFloat(selectedInvoice.discountAmount).toFixed(2)}</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2 border-l pl-4">
                      <div className="flex justify-between font-semibold text-lg">
                        <span>Total Amount:</span>
                        <span>₹{parseFloat(selectedInvoice.totalAmount).toLocaleString("en-IN")}</span>
                      </div>
                      <div className="flex justify-between text-green-600">
                        <span>Amount Paid:</span>
                        <span>₹{parseFloat(selectedInvoice.paidAmount).toLocaleString("en-IN")}</span>
                      </div>
                      <div className={`flex justify-between font-semibold text-lg ${
                        parseFloat(selectedInvoice.balanceAmount) > 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        <span>Balance Due:</span>
                        <span>₹{parseFloat(selectedInvoice.balanceAmount).toLocaleString("en-IN")}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {selectedInvoice.notes && (
                <div className="border-t pt-4">
                  <h3 className="font-semibold mb-2">Notes</h3>
                  <p>{selectedInvoice.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
