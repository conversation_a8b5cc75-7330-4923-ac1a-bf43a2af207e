import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Search, Edit, Trash2, Package } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { fetchInventoryItems, createInventoryItem, updateInventoryItem, deleteInventoryItem, fetchNextItemCode } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { PURITY_REFERENCE, getPurityPercentage } from "@/lib/jewelry-calculations";

export default function Inventory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [selectedMetalType, setSelectedMetalType] = useState("");
  const [selectedPurity, setSelectedPurity] = useState("");
  const [generatedItemCode, setGeneratedItemCode] = useState("");
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get purity options based on metal type - Industry Standard Percentages
  const getPurityOptions = (metalType: string) => {
    const metalPurities = PURITY_REFERENCE[metalType as keyof typeof PURITY_REFERENCE];
    if (!metalPurities) return [];

    return Object.entries(metalPurities).map(([code, info]) => ({
      value: code,
      label: `${code} (${info.percentage}% - ${info.label})`,
      percentage: info.percentage
    }));
  };

  const { data: inventoryItems, isLoading } = useQuery({
    queryKey: ["/api/inventory"],
    queryFn: fetchInventoryItems,
    staleTime: 0, // Always consider data stale to ensure fresh data
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  const createItemMutation = useMutation({
    mutationFn: createInventoryItem,
    onSuccess: (data) => {
      // Show success message with item details
      toast({
        title: "✅ Success",
        description: `Inventory item "${data.itemName}" created successfully with code ${data.itemCode}`,
      });

      // Refresh the inventory list and close dialog
      queryClient.invalidateQueries({ queryKey: ["/api/inventory"] });
      setIsDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      console.error("Error creating inventory item:", error);

      // Parse error message for better user feedback
      let errorMessage = "Failed to create inventory item";

      if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast({
        title: "❌ Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const updateItemMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => updateInventoryItem(id, data),
    onSuccess: (data) => {
      // Show success message with item details
      toast({
        title: "✅ Success",
        description: `Inventory item "${data.itemName}" updated successfully`,
      });

      // Refresh the inventory list and close dialog
      queryClient.invalidateQueries({ queryKey: ["/api/inventory"] });
      setIsDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      console.error("Error updating inventory item:", error);

      // Parse error message for better user feedback
      let errorMessage = "Failed to update inventory item";

      if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast({
        title: "❌ Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const deleteItemMutation = useMutation({
    mutationFn: deleteInventoryItem,
    onSuccess: () => {
      // Show success message
      toast({
        title: "✅ Success",
        description: "Inventory item deleted successfully",
      });

      // Refresh the inventory list
      queryClient.invalidateQueries({ queryKey: ["/api/inventory"] });
    },
    onError: (error: any) => {
      console.error("Error deleting inventory item:", error);

      // Parse error message for better user feedback
      let errorMessage = "Failed to delete inventory item";

      if (error?.message) {
        errorMessage = error.message;

        // If item was already deleted, refresh the list to show current state
        if (errorMessage.includes("already deleted") || errorMessage.includes("not found")) {
          toast({
            title: "ℹ️ Info",
            description: "This item was already deleted. Refreshing the list...",
          });

          // Refresh the inventory list to show current state
          queryClient.invalidateQueries({ queryKey: ["/api/inventory"] });
          return;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast({
        title: "❌ Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const filteredItems = inventoryItems?.filter((item: any) =>
    item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.itemCode.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleMetalTypeChange = async (metalType: string) => {
    setSelectedMetalType(metalType);
    setSelectedPurity(""); // Reset purity when metal type changes

    // Show loading state
    setGeneratedItemCode("Generating...");

    try {
      const response = await fetchNextItemCode(metalType);
      setGeneratedItemCode(response.itemCode);

      // Update the form field
      const itemCodeInput = document.getElementById("itemCode") as HTMLInputElement;
      if (itemCodeInput) {
        itemCodeInput.value = response.itemCode;
      }

      // Show success feedback
      toast({
        title: "✅ Code Generated",
        description: `Auto-generated item code: ${response.itemCode}`,
      });

    } catch (error) {
      console.error("Error generating item code:", error);
      setGeneratedItemCode("");

      toast({
        title: "⚠️ Warning",
        description: "Could not auto-generate item code. Please enter manually.",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setSelectedMetalType("");
    setSelectedPurity("");
    setGeneratedItemCode("");
    setIsEditMode(false);
    setEditingItem(null);
    const form = document.querySelector('form') as HTMLFormElement;
    if (form) {
      form.reset();
    }
  };

  const handleEdit = (item: any) => {
    setIsEditMode(true);
    setEditingItem(item);
    setSelectedMetalType(item.metalType);
    setSelectedPurity(item.purity);
    setGeneratedItemCode(item.itemCode);
    setIsDialogOpen(true);
  };

  const handleDelete = (item: any) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete "${item.itemName}" (${item.itemCode})?\n\nThis action cannot be undone.`
    );

    if (confirmed) {
      deleteItemMutation.mutate(item.id);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    // Use the controlled values for Select components
    const data = {
      itemCode: generatedItemCode || formData.get("itemCode") || "",
      itemName: formData.get("itemName"),
      designName: formData.get("designName"),
      metalType: selectedMetalType || formData.get("metalType"),
      purity: selectedPurity || formData.get("purity"),
      purityPercentage: getPurityPercentage(selectedMetalType, selectedPurity).toString() || "96",
      grossWeight: formData.get("grossWeight"),
      netWeight: formData.get("netWeight"),
      stoneWeight: formData.get("stoneWeight") || "0",
      stoneAmount: formData.get("stoneAmount") || "0",
      wastagePercentage: formData.get("wastagePercentage") || "0",
      makingChargesRate: formData.get("makingChargesRate") || "0",
      makingCharges: formData.get("makingCharges") || "0",
      stoneCharges: formData.get("stoneCharges") || "0",
      valueAdditionPercentage: formData.get("valueAdditionPercentage") || "0",
      hsnCode: formData.get("hsnCode"),
      barcode: formData.get("barcode"),
      quantity: parseInt(formData.get("quantity") as string) || 1,
    };

    // Enhanced validation
    const errors: string[] = [];

    if (!data.itemCode?.trim()) errors.push("Item code is required");
    if (!data.itemName?.trim()) errors.push("Item name is required");
    if (!data.designName?.trim()) errors.push("Design name is required");
    if (!data.metalType) errors.push("Metal type is required");
    if (!data.purity) errors.push("Purity is required");
    if (!data.grossWeight || isNaN(parseFloat(data.grossWeight as string)) || parseFloat(data.grossWeight as string) <= 0) {
      errors.push("Gross weight must be a positive number");
    }
    if (!data.netWeight || isNaN(parseFloat(data.netWeight as string)) || parseFloat(data.netWeight as string) <= 0) {
      errors.push("Net weight must be a positive number");
    }
    if (!data.hsnCode?.trim()) errors.push("HSN code is required");

    if (errors.length > 0) {
      toast({
        title: "❌ Validation Error",
        description: errors.join(", "),
        variant: "destructive",
      });
      return;
    }

    console.log("Submitting inventory item:", data); // Debug log

    if (isEditMode && editingItem) {
      updateItemMutation.mutate({ id: editingItem.id, data });
    } else {
      createItemMutation.mutate(data);
    }
  };

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Inventory Management" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search items by name or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={(open) => {
              setIsDialogOpen(open);
              if (!open) resetForm();
            }}>
              <DialogTrigger asChild>
                <Button className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {isEditMode ? "Edit Inventory Item" : "Add New Inventory Item"}
                  </DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Hidden inputs to ensure Select values are captured */}
                  <input type="hidden" name="metalType" value={selectedMetalType} />
                  <input type="hidden" name="purity" value={selectedPurity} />
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="itemCode">Item Code</Label>
                      <div className="flex space-x-2">
                        <Input
                          id="itemCode"
                          name="itemCode"
                          value={generatedItemCode}
                          onChange={(e) => setGeneratedItemCode(e.target.value)}
                          placeholder="Auto-generated when metal type is selected"
                          className={generatedItemCode === "Generating..." ? "animate-pulse" : ""}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => selectedMetalType && handleMetalTypeChange(selectedMetalType)}
                          disabled={!selectedMetalType || generatedItemCode === "Generating..."}
                          title={!selectedMetalType ? "Select metal type first" : "Generate new code"}
                        >
                          {generatedItemCode === "Generating..." ? "⏳" : "🔄"}
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="itemName">Item Name</Label>
                      <Input
                        id="itemName"
                        name="itemName"
                        required
                        defaultValue={editingItem?.itemName || ""}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="designName">Design Name</Label>
                      <Input
                        id="designName"
                        name="designName"
                        defaultValue={editingItem?.designName || ""}
                      />
                    </div>
                    <div>
                      <Label htmlFor="metalType">Metal Type</Label>
                      <Select
                        name="metalType"
                        required
                        onValueChange={handleMetalTypeChange}
                        value={selectedMetalType}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select metal type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gold">Gold</SelectItem>
                          <SelectItem value="silver">Silver</SelectItem>
                          <SelectItem value="platinum">Platinum</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="purity">Purity</Label>
                      <Select
                        name="purity"
                        required
                        onValueChange={setSelectedPurity}
                        value={selectedPurity}
                        disabled={!selectedMetalType}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={selectedMetalType ? "Select purity" : "Select metal type first"} />
                        </SelectTrigger>
                        <SelectContent>
                          {getPurityOptions(selectedMetalType).map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="grossWeight">Gross Weight (g)</Label>
                      <Input
                        id="grossWeight"
                        name="grossWeight"
                        type="number"
                        step="0.001"
                        required
                        defaultValue={editingItem?.grossWeight || ""}
                      />
                    </div>
                    <div>
                      <Label htmlFor="netWeight">Net Weight (g)</Label>
                      <Input
                        id="netWeight"
                        name="netWeight"
                        type="number"
                        step="0.001"
                        required
                        defaultValue={editingItem?.netWeight || ""}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="stoneWeight">Stone Weight (g)</Label>
                      <Input
                        id="stoneWeight"
                        name="stoneWeight"
                        type="number"
                        step="0.001"
                        min="0"
                        defaultValue={editingItem?.stoneWeight || "0"}
                      />
                    </div>
                    <div>
                      <Label htmlFor="stoneAmount">Stone Amount (₹)</Label>
                      <Input
                        id="stoneAmount"
                        name="stoneAmount"
                        type="number"
                        step="0.01"
                        min="0"
                        defaultValue={editingItem?.stoneAmount || "0"}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="wastagePercentage">Wastage % (e.g., 1)</Label>
                      <Input
                        id="wastagePercentage"
                        name="wastagePercentage"
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        defaultValue={editingItem?.wastagePercentage || "0"}
                      />
                    </div>
                    <div>
                      <Label htmlFor="makingChargesRate">Making Charges Rate (₹/g)</Label>
                      <Input
                        id="makingChargesRate"
                        name="makingChargesRate"
                        type="number"
                        step="0.01"
                        min="0"
                        defaultValue={editingItem?.makingChargesRate || "0"}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="makingCharges">Making Charges (₹)</Label>
                      <Input
                        id="makingCharges"
                        name="makingCharges"
                        type="number"
                        step="0.01"
                        defaultValue={editingItem?.makingCharges || ""}
                      />
                    </div>
                    <div>
                      <Label htmlFor="hsnCode">HSN Code</Label>
                      <Input
                        id="hsnCode"
                        name="hsnCode"
                        required
                        defaultValue={editingItem?.hsnCode || ""}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="barcode">Barcode</Label>
                      <Input
                        id="barcode"
                        name="barcode"
                        defaultValue={editingItem?.barcode || ""}
                      />
                    </div>
                    <div>
                      <Label htmlFor="quantity">Quantity</Label>
                      <Input
                        id="quantity"
                        name="quantity"
                        type="number"
                        min="1"
                        defaultValue={editingItem?.quantity || "1"}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => {
                      setIsDialogOpen(false);
                      resetForm();
                    }}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={createItemMutation.isPending || updateItemMutation.isPending}>
                      {isEditMode
                        ? (updateItemMutation.isPending ? "Updating..." : "Update Item")
                        : (createItemMutation.isPending ? "Creating..." : "Create Item")
                      }
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Inventory Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-neutral-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-neutral-200 rounded w-1/2 mb-4"></div>
                      <div className="h-3 bg-neutral-200 rounded w-full mb-2"></div>
                      <div className="h-3 bg-neutral-200 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredItems.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-neutral-700 mb-2">No inventory items found</h3>
                <p className="text-neutral-500 mb-4">
                  {searchTerm ? "No items match your search criteria." : "Start by adding your first inventory item."}
                </p>
                <Button onClick={() => setIsDialogOpen(true)} className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Item
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item: any) => (
                <Card key={item.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{item.itemName}</CardTitle>
                        <p className="text-sm text-neutral-600">{item.itemCode}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(item)}
                          title="Edit item"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(item)}
                          disabled={deleteItemMutation.isPending}
                          title="Delete item"
                          className="hover:bg-red-50 hover:border-red-200"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-neutral-600">Metal:</span>
                        <Badge variant="outline">{item.metalType} ({item.purity})</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-neutral-600">Gross Weight:</span>
                        <span className="text-sm font-medium">{item.grossWeight}g</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-neutral-600">Net Weight:</span>
                        <span className="text-sm font-medium">{item.netWeight}g</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-neutral-600">Making Charges:</span>
                        <span className="text-sm font-medium">₹{item.makingCharges}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-neutral-600">Quantity:</span>
                        <Badge variant={item.quantity <= 5 ? "destructive" : "secondary"}>
                          {item.quantity}
                        </Badge>
                      </div>
                      {item.designName && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-neutral-600">Design:</span>
                          <span className="text-sm">{item.designName}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
