import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/api";

const companySettingsSchema = z.object({
  name: z.string().min(1, "Company name is required"),
  address: z.string().min(1, "Address is required"),
  phone: z.string().min(10, "Valid phone number is required"),
  email: z.string().email("Valid email is required"),
  gstin: z.string().min(15, "Valid GSTIN is required").max(15, "GSTIN must be 15 characters"),
  pan: z.string().min(10, "Valid PAN is required").max(10, "PAN must be 10 characters"),
  stateCode: z.string().min(1, "State code is required"),
  website: z.string().optional(),
  bankName: z.string().min(1, "Bank name is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  ifscCode: z.string().min(11, "Valid IFSC code is required").max(11, "IFSC must be 11 characters"),
  branch: z.string().min(1, "Branch name is required"),
});

type CompanySettingsForm = z.infer<typeof companySettingsSchema>;

interface CompanySettingsProps {
  onSave?: (data: CompanySettingsForm) => void;
}

// API function to fetch company settings
async function fetchCompanySettings() {
  const response = await apiRequest("GET", "/api/settings?category=company");
  return response.json();
}

export default function CompanySettings({ onSave }: CompanySettingsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch company settings from database
  const { data: companySettings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ["/api/settings", "company"],
    queryFn: fetchCompanySettings,
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<CompanySettingsForm>({
    resolver: zodResolver(companySettingsSchema),
    defaultValues: {
      name: "Shree Jewelry House",
      address: "123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      gstin: "33AAAAA0000A1Z5",
      pan: "**********",
      stateCode: "33",
      website: "www.shreejewelry.com",
      bankName: "State Bank of India",
      accountNumber: "**************",
      ifscCode: "SBIN0001234",
      branch: "T. Nagar Branch",
    },
  });

  // Helper function to get setting value from database
  const getSettingValue = (key: string, defaultValue: string = "") => {
    // Handle key mapping for legacy database entries
    const keyMap: { [key: string]: string } = {
      'gstin': 'gst_number', // Map gstin to legacy gst_number key
    };

    const dbKey = keyMap[key] || key;
    const setting = companySettings?.find((s: any) => s.key === dbKey || s.key === key);
    if (!setting) return defaultValue;

    let value = setting.value;
    // Handle JSON-encoded strings
    if (typeof value === 'string' && (value.startsWith('"') || value.startsWith('{'))) {
      try {
        value = JSON.parse(value);
      } catch {
        // If parsing fails, use the string as-is
      }
    }
    return value || defaultValue;
  };

  // Load settings from database when available
  useEffect(() => {
    if (companySettings && companySettings.length > 0) {
      console.log('Loading company settings from database:', companySettings);

      // Map database settings to form fields
      const settingsMap: Partial<CompanySettingsForm> = {
        name: getSettingValue("name", "Shree Jewelry House"),
        address: getSettingValue("address", "123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu"),
        phone: getSettingValue("phone", "+91 98765 43210"),
        email: getSettingValue("email", "<EMAIL>"),
        gstin: getSettingValue("gstin", "33AAAAA0000A1Z5"), // This will map to gst_number in database
        pan: getSettingValue("pan", "**********"),
        stateCode: getSettingValue("stateCode", "33"),
        website: getSettingValue("website", "www.shreejewelry.com"),
        bankName: getSettingValue("bankName", "State Bank of India"),
        accountNumber: getSettingValue("accountNumber", "**************"),
        ifscCode: getSettingValue("ifscCode", "SBIN0001234"),
        branch: getSettingValue("branch", "T. Nagar Branch"),
      };

      console.log('Mapped settings:', settingsMap);

      // Update form values
      Object.entries(settingsMap).forEach(([key, value]) => {
        if (value) {
          console.log(`Setting form field ${key} to:`, value);
          setValue(key as keyof CompanySettingsForm, value);
        }
      });
    } else {
      console.log('No database settings found, checking localStorage');
      // Fallback to localStorage if no database settings
      const savedSettings = localStorage.getItem('companySettings');
      if (savedSettings) {
        try {
          const settings = JSON.parse(savedSettings);
          console.log('Loading from localStorage:', settings);
          Object.keys(settings).forEach((key) => {
            setValue(key as keyof CompanySettingsForm, settings[key]);
          });
        } catch (error) {
          console.error('Error loading company settings from localStorage:', error);
        }
      }
    }
  }, [companySettings, setValue]);

  const onSubmit = async (data: CompanySettingsForm) => {
    setIsLoading(true);
    try {
      // Save to localStorage
      localStorage.setItem('companySettings', JSON.stringify(data));
      
      // Call parent callback if provided
      if (onSave) {
        onSave(data);
      }

      toast({
        title: "Settings Saved",
        description: "Company settings have been updated successfully.",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateGSTIN = (gstin: string) => {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstin);
  };

  const validatePAN = (pan: string) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
  };

  const validateIFSC = (ifsc: string) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(ifsc);
  };

  // Show loading state while fetching settings
  if (isLoadingSettings) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Company Settings</h1>
            <p className="text-gray-600">Loading company settings...</p>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Company Settings</h1>
          <p className="text-gray-600">Configure your company details for GST-compliant invoices</p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Debug info - remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-blue-50 border border-blue-200 rounded p-3 text-sm">
            <strong>Debug Info:</strong>
            <br />Settings loaded: {companySettings ? companySettings.length : 0} items
            <br />Current name value: {watch("name")}
            <br />Current email value: {watch("email")}
          </div>
        )}

        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Company Name *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="Enter company name"
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  {...register("website")}
                  placeholder="www.yourcompany.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="address">Address *</Label>
              <Textarea
                id="address"
                {...register("address")}
                placeholder="Enter complete address"
                rows={3}
              />
              {errors.address && (
                <p className="text-sm text-red-600 mt-1">{errors.address.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  {...register("phone")}
                  placeholder="+91 98765 43210"
                />
                {errors.phone && (
                  <p className="text-sm text-red-600 mt-1">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* GST Information */}
        <Card>
          <CardHeader>
            <CardTitle>GST Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="gstin">GSTIN *</Label>
                <Input
                  id="gstin"
                  {...register("gstin")}
                  placeholder="33AAAAA0000A1Z5"
                  maxLength={15}
                  onChange={(e) => {
                    const value = e.target.value.toUpperCase();
                    setValue("gstin", value);
                  }}
                />
                {errors.gstin && (
                  <p className="text-sm text-red-600 mt-1">{errors.gstin.message}</p>
                )}
                {watch("gstin") && !validateGSTIN(watch("gstin")) && (
                  <p className="text-sm text-orange-600 mt-1">Invalid GSTIN format</p>
                )}
              </div>

              <div>
                <Label htmlFor="pan">PAN Number *</Label>
                <Input
                  id="pan"
                  {...register("pan")}
                  placeholder="**********"
                  maxLength={10}
                  onChange={(e) => {
                    const value = e.target.value.toUpperCase();
                    setValue("pan", value);
                  }}
                />
                {errors.pan && (
                  <p className="text-sm text-red-600 mt-1">{errors.pan.message}</p>
                )}
                {watch("pan") && !validatePAN(watch("pan")) && (
                  <p className="text-sm text-orange-600 mt-1">Invalid PAN format</p>
                )}
              </div>

              <div>
                <Label htmlFor="stateCode">State Code *</Label>
                <Input
                  id="stateCode"
                  {...register("stateCode")}
                  placeholder="33"
                  maxLength={2}
                />
                {errors.stateCode && (
                  <p className="text-sm text-red-600 mt-1">{errors.stateCode.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bank Details */}
        <Card>
          <CardHeader>
            <CardTitle>Bank Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bankName">Bank Name *</Label>
                <Input
                  id="bankName"
                  {...register("bankName")}
                  placeholder="State Bank of India"
                />
                {errors.bankName && (
                  <p className="text-sm text-red-600 mt-1">{errors.bankName.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="branch">Branch *</Label>
                <Input
                  id="branch"
                  {...register("branch")}
                  placeholder="T. Nagar Branch"
                />
                {errors.branch && (
                  <p className="text-sm text-red-600 mt-1">{errors.branch.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="accountNumber">Account Number *</Label>
                <Input
                  id="accountNumber"
                  {...register("accountNumber")}
                  placeholder="**************"
                />
                {errors.accountNumber && (
                  <p className="text-sm text-red-600 mt-1">{errors.accountNumber.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="ifscCode">IFSC Code *</Label>
                <Input
                  id="ifscCode"
                  {...register("ifscCode")}
                  placeholder="SBIN0001234"
                  maxLength={11}
                  onChange={(e) => {
                    const value = e.target.value.toUpperCase();
                    setValue("ifscCode", value);
                  }}
                />
                {errors.ifscCode && (
                  <p className="text-sm text-red-600 mt-1">{errors.ifscCode.message}</p>
                )}
                {watch("ifscCode") && !validateIFSC(watch("ifscCode")) && (
                  <p className="text-sm text-orange-600 mt-1">Invalid IFSC format</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          {process.env.NODE_ENV === 'development' && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                console.log('Manual reload triggered');
                console.log('Company settings:', companySettings);
                if (companySettings && companySettings.length > 0) {
                  const settingsMap: Partial<CompanySettingsForm> = {
                    name: getSettingValue("name", "Shree Jewelry House"),
                    address: getSettingValue("address", "123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu"),
                    phone: getSettingValue("phone", "+91 98765 43210"),
                    email: getSettingValue("email", "<EMAIL>"),
                    gstin: getSettingValue("gstin", "33AAAAA0000A1Z5"),
                    pan: getSettingValue("pan", "**********"),
                    stateCode: getSettingValue("stateCode", "33"),
                    website: getSettingValue("website", "www.shreejewelry.com"),
                    bankName: getSettingValue("bankName", "State Bank of India"),
                    accountNumber: getSettingValue("accountNumber", "**************"),
                    ifscCode: getSettingValue("ifscCode", "SBIN0001234"),
                    branch: getSettingValue("branch", "T. Nagar Branch"),
                  };
                  Object.entries(settingsMap).forEach(([key, value]) => {
                    if (value) {
                      setValue(key as keyof CompanySettingsForm, value);
                    }
                  });
                }
              }}
            >
              Reload Form Data
            </Button>
          )}
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
          >
            {isLoading ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </form>
    </div>
  );
}