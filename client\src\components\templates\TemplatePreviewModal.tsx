import React, { useState } from 'react';
import { X, Download, FileText, Eye } from 'lucide-react';
import { InvoiceTemplate } from '../../types/template';

interface TemplatePreviewModalProps {
  template: InvoiceTemplate;
  onClose: () => void;
}

export const TemplatePreviewModal: React.FC<TemplatePreviewModalProps> = ({
  template,
  onClose
}) => {
  const [previewMode, setPreviewMode] = useState<'visual' | 'code'>('visual');

  // Sample invoice data for preview
  const sampleData = {
    invoice: {
      id: 1,
      invoiceNumber: "INV-2024-001",
      invoiceDate: "2024-01-15",
      dueDate: "2024-02-14",
      subtotal: "50000.00",
      cgstAmount: "750.00",
      sgstAmount: "750.00",
      igstAmount: "0.00",
      discountAmount: "0.00",
      totalAmount: "51500.00",
      paidAmount: "0.00",
      balanceAmount: "51500.00",
      status: "pending",
      paymentTerms: "30 Days",
      notes: "Premium jewelry collection"
    },
    customer: {
      id: 1,
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "+91 9876543210",
      address: "123 MG Road, Bangalore, Karnataka 560001",
      gstin: "29**********1Z5",
      pan: "**********",
      stateCode: "29"
    },
    items: [
      {
        id: 1,
        itemId: 101,
        itemName: "Gold Necklace Set",
        quantity: 1,
        grossWeight: "25.500",
        netWeight: "23.750",
        fineWeight: "21.750",
        stoneWeight: "1.750",
        stoneAmount: "2500.00",
        purityPercentage: "91.6",
        wastagePercentage: "8.0",
        ratePerGram: "6500.00",
        goldValue: "154375.00",
        makingCharges: "15000.00",
        stoneCharges: "2500.00",
        itemTotal: "171875.00",
        taxableAmount: "50000.00",
        cgstAmount: "750.00",
        sgstAmount: "750.00",
        totalAmount: "51500.00",
        metalType: "Gold",
        purity: "22K",
        hsnCode: "7113"
      }
    ],
    company: {
      name: "PREMIUM JEWELERS",
      address: "456 Commercial Street, Bangalore, Karnataka 560001",
      phone: "+91 80 ********",
      email: "<EMAIL>",
      gstin: "29**********1Z5",
      pan: "**********",
      stateCode: "29",
      website: "www.premiumjewelers.com",
      bankDetails: {
        bankName: "State Bank of India",
        accountNumber: "********90123456",
        ifscCode: "SBIN0001234",
        branch: "Commercial Street Branch"
      }
    },
    metalRates: {
      gold: 6500,
      silver: 85,
      date: "2024-01-15"
    }
  };

  const generatePreviewHTML = () => {
    const { colors, layout, settings } = template;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Invoice Preview - ${template.name}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: ${layout.margin}mm;
            background: white;
            color: rgb(${colors.dark.join(',')});
            font-size: ${layout.fontSize.body}pt;
          }
          .header {
            background: rgb(${colors.primary.join(',')});
            color: white;
            padding: 10px;
            height: ${layout.headerHeight}mm;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: ${layout.sectionSpacing}mm;
          }
          .company-name {
            font-size: ${layout.fontSize.header}pt;
            font-weight: bold;
          }
          .invoice-title {
            font-size: ${layout.fontSize.subheader}pt;
            font-weight: bold;
          }
          .section {
            margin-bottom: ${layout.sectionSpacing}mm;
            padding: 8px;
            background: rgb(${colors.lightGray.join(',')});
            border: 1px solid rgb(${colors.mediumGray.join(',')});
          }
          .section-title {
            font-size: ${layout.fontSize.subheader}pt;
            font-weight: bold;
            color: rgb(${colors.primary.join(',')});
            margin-bottom: 5px;
          }
          .two-column {
            display: flex;
            gap: 20px;
          }
          .column {
            flex: 1;
          }
          .table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
          }
          .table th {
            background: rgb(${colors.dark.join(',')});
            color: white;
            padding: 8px;
            text-align: left;
            font-size: ${layout.fontSize.small}pt;
          }
          .table td {
            padding: 6px 8px;
            border-bottom: 1px solid rgb(${colors.lightGray.join(',')});
            font-size: ${layout.fontSize.small}pt;
          }
          .summary {
            background: rgb(${colors.accent.join(',')});
            color: white;
            padding: 10px;
            text-align: right;
            font-weight: bold;
          }
          .compact {
            ${settings.compactMode ? 'line-height: 1.2; padding: 4px;' : 'line-height: 1.5; padding: 8px;'}
          }
          .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48pt;
            color: rgba(${colors.mediumGray.join(',')}, 0.1);
            z-index: -1;
            pointer-events: none;
          }
        </style>
      </head>
      <body>
        ${settings.showWatermark ? `<div class="watermark">${template.customFields?.watermarkText || 'PREVIEW'}</div>` : ''}
        
        <div class="header">
          <div>
            <div class="company-name">${sampleData.company.name}</div>
            <div style="font-size: ${layout.fontSize.small}pt;">Premium Jewelry & Ornaments</div>
          </div>
          <div style="text-align: right;">
            <div class="invoice-title">TAX INVOICE</div>
            <div>Invoice #${sampleData.invoice.invoiceNumber}</div>
          </div>
        </div>

        <div class="section compact">
          <div class="two-column">
            <div class="column">
              <div class="section-title">FROM:</div>
              <div><strong>${sampleData.company.name}</strong></div>
              <div>${sampleData.company.address}</div>
              <div>Phone: ${sampleData.company.phone}</div>
              <div>GSTIN: ${sampleData.company.gstin}</div>
            </div>
            <div class="column">
              <div class="section-title">BILL TO:</div>
              <div><strong>${sampleData.customer.name}</strong></div>
              <div>${sampleData.customer.address}</div>
              <div>Phone: ${sampleData.customer.phone}</div>
              <div>GSTIN: ${sampleData.customer.gstin}</div>
            </div>
          </div>
        </div>

        <div class="section compact">
          <div style="background: rgb(${colors.primary.join(',')}); color: white; padding: 5px; margin-bottom: 10px;">
            <strong>Invoice Details</strong>
          </div>
          <div class="two-column">
            <div>Date: ${sampleData.invoice.invoiceDate}</div>
            <div>Due Date: ${sampleData.invoice.dueDate}</div>
            <div>Gold Rate: ${settings.currency}${sampleData.metalRates.gold}/g</div>
            <div>Terms: ${sampleData.invoice.paymentTerms}</div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>#</th>
              <th>Item Description</th>
              <th>Qty</th>
              <th>Weight (g)</th>
              <th>Rate/g</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${sampleData.items.map((item, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>
                  <strong>${item.itemName}</strong><br>
                  <small>${item.metalType} ${item.purity} - HSN: ${item.hsnCode}</small>
                </td>
                <td>${item.quantity}</td>
                <td>
                  Gross: ${item.grossWeight}<br>
                  Net: ${item.netWeight}
                </td>
                <td>${settings.currency}${parseFloat(item.ratePerGram).toLocaleString()}</td>
                <td style="color: rgb(${colors.accent.join(',')});">
                  <strong>${settings.currency}${parseFloat(item.totalAmount).toLocaleString()}</strong>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div style="text-align: right; margin: 20px 0;">
          <div style="display: inline-block; background: rgb(${colors.lightGray.join(',')}); padding: 15px; border: 1px solid rgb(${colors.primary.join(',')});">
            <div>Subtotal: ${settings.currency}${parseFloat(sampleData.invoice.subtotal).toLocaleString()}</div>
            <div>CGST @ 1.5%: ${settings.currency}${parseFloat(sampleData.invoice.cgstAmount).toLocaleString()}</div>
            <div>SGST @ 1.5%: ${settings.currency}${parseFloat(sampleData.invoice.sgstAmount).toLocaleString()}</div>
            <hr style="border-color: rgb(${colors.primary.join(',')});">
            <div class="summary" style="background: rgb(${colors.accent.join(',')});">
              <strong>TOTAL: ${settings.currency}${parseFloat(sampleData.invoice.totalAmount).toLocaleString()}</strong>
            </div>
          </div>
        </div>

        ${settings.showBankDetails ? `
          <div class="section compact">
            <div class="section-title">BANK DETAILS</div>
            <div class="two-column">
              <div>
                <div>Bank: ${sampleData.company.bankDetails?.bankName}</div>
                <div>Account: ${sampleData.company.bankDetails?.accountNumber}</div>
              </div>
              <div>
                <div>IFSC: ${sampleData.company.bankDetails?.ifscCode}</div>
                <div>Branch: ${sampleData.company.bankDetails?.branch}</div>
              </div>
            </div>
          </div>
        ` : ''}

        ${settings.showTermsAndConditions ? `
          <div class="section compact">
            <div class="section-title">TERMS & CONDITIONS</div>
            <div style="font-size: ${layout.fontSize.small}pt;">
              <div>• Payment due within 30 days</div>
              <div>• Goods once sold will not be taken back</div>
              <div>• Subject to local jurisdiction</div>
            </div>
          </div>
        ` : ''}

        <div style="text-align: center; margin-top: 20px; padding-top: 10px; border-top: 2px solid rgb(${colors.primary.join(',')});">
          <div style="color: rgb(${colors.primary.join(',')}); font-weight: bold;">Thank you for your business!</div>
          <div style="font-size: ${layout.fontSize.small}pt; color: rgb(${colors.mediumGray.join(',')});">
            ${sampleData.company.phone} | ${sampleData.company.email} | ${sampleData.company.website}
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const handleDownloadPreview = () => {
    const html = generatePreviewHTML();
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${template.name.replace(/\s+/g, '_')}_preview.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Template Preview: {template.name}
            </h2>
            <p className="text-sm text-gray-600 mt-1">{template.description}</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode('visual')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  previewMode === 'visual'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Eye className="w-4 h-4 inline mr-1" />
                Visual
              </button>
              <button
                onClick={() => setPreviewMode('code')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  previewMode === 'code'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FileText className="w-4 h-4 inline mr-1" />
                Code
              </button>
            </div>
            <button
              onClick={handleDownloadPreview}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Download className="w-4 h-4" />
              Download
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-auto max-h-[calc(90vh-120px)]">
          {previewMode === 'visual' ? (
            <div className="p-6">
              <div className="bg-white border rounded-lg overflow-hidden">
                <iframe
                  srcDoc={generatePreviewHTML()}
                  className="w-full h-[800px] border-0"
                  title="Template Preview"
                />
              </div>
            </div>
          ) : (
            <div className="p-6">
              <div className="bg-gray-900 text-gray-100 rounded-lg overflow-hidden">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="text-sm font-medium text-gray-300">Template Configuration</h3>
                </div>
                <pre className="p-4 text-xs overflow-auto max-h-[600px]">
                  <code>{JSON.stringify(template, null, 2)}</code>
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Template Info */}
        <div className="border-t bg-gray-50 p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Created:</span>
              <div className="font-medium">{new Date(template.createdAt).toLocaleDateString()}</div>
            </div>
            <div>
              <span className="text-gray-600">Updated:</span>
              <div className="font-medium">{new Date(template.updatedAt).toLocaleDateString()}</div>
            </div>
            <div>
              <span className="text-gray-600">Type:</span>
              <div className="font-medium capitalize">{template.createdBy}</div>
            </div>
            <div>
              <span className="text-gray-600">Status:</span>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  template.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {template.isActive ? 'Active' : 'Inactive'}
                </span>
                {template.isDefault && (
                  <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                    Default
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};