import { Link, useLocation } from "wouter";
import { Gem, LayoutDashboard, Coins, Package, Receipt, Users, Wallet, TrendingUp, FileText, Settings, UserCog } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/auth-context";

const navigation = [
  { name: "Dashboard", href: "/", icon: LayoutDashboard },
  { name: "Metal Procurement", href: "/procurement", icon: Coins, roles: ["admin", "manager"] },
  { name: "Inventory", href: "/inventory", icon: Package },
  { name: "Billing", href: "/billing", icon: Receipt },
  { name: "Customers", href: "/customers", icon: Users },
  { name: "Payments", href: "/payments", icon: Wallet },
];

const management = [
  { name: "Gold/Silver Rates", href: "/rates", icon: TrendingUp, roles: ["admin", "manager"] },
  { name: "Reports", href: "/reports", icon: FileText },
  { name: "Settings", href: "/settings", icon: Settings, roles: ["admin", "manager"] },
  { name: "User Management", href: "/users", icon: UserCog, roles: ["admin"] },
];

export default function Sidebar() {
  const [location] = useLocation();
  const { hasRole } = useAuth();

  return (
    <div className="w-64 bg-[hsl(154,50%,20%)] text-white shadow-lg flex-shrink-0">
      <div className="p-6 border-b border-[hsl(154,46%,35%)]">
        <h1 className="text-xl font-bold text-[hsl(38,92%,50%)]">
          <Gem className="inline mr-2" size={20} />
          JewelPro Billing
        </h1>
        <p className="text-sm text-neutral-300 mt-1">Tamil Nadu</p>
      </div>
      
      <nav className="mt-6">
        <div className="px-4 mb-4">
          <p className="text-xs font-semibold text-neutral-300 uppercase tracking-wider">MAIN MODULES</p>
        </div>
        
        <ul className="space-y-1">
          {navigation.map((item) => {
            const Icon = item.icon;
            const isActive = location === item.href;

            // Check if user has required role for this item
            if (item.roles && !hasRole(item.roles)) {
              return null;
            }

            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center px-4 py-3 text-sm rounded-r-lg mr-2 transition-colors",
                    isActive
                      ? "bg-[hsl(154,46%,35%)] text-white"
                      : "text-neutral-300 hover:text-white hover:bg-[hsl(154,46%,35%)]"
                  )}
                >
                  <Icon className="w-5 mr-3" />
                  {item.name}
                </Link>
              </li>
            );
          })}
        </ul>

        <div className="px-4 mb-4 mt-8">
          <p className="text-xs font-semibold text-neutral-300 uppercase tracking-wider">MANAGEMENT</p>
        </div>

        <ul className="space-y-1">
          {management.map((item) => {
            const Icon = item.icon;
            const isActive = location === item.href;

            // Check if user has required role for this item
            if (item.roles && !hasRole(item.roles)) {
              return null;
            }

            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center px-4 py-3 text-sm rounded-r-lg mr-2 transition-colors",
                    isActive
                      ? "bg-[hsl(154,46%,35%)] text-white"
                      : "text-neutral-300 hover:text-white hover:bg-[hsl(154,46%,35%)]"
                  )}
                >
                  <Icon className="w-5 mr-3" />
                  {item.name}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}