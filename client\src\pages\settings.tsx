import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Settings as SettingsIcon, User, Building, Palette, Database, Shield, Bell, Globe } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import CompanySettings from "@/components/billing/company-settings";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/api";

// API functions
async function fetchSettings(category?: string) {
  const url = category ? `/api/settings?category=${category}` : "/api/settings";
  const response = await apiRequest("GET", url);
  return response.json();
}

async function saveSetting(category: string, key: string, value: any, description?: string) {
  const response = await apiRequest("POST", "/api/settings", {
    category,
    key,
    value,
    description
  });
  return response.json();
}

export default function Settings() {
  const [activeTab, setActiveTab] = useState("general");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all settings (not just for active tab)
  const { data: allSettings, isLoading } = useQuery({
    queryKey: ["/api/settings"],
    queryFn: () => fetchSettings(), // Fetch all settings
  });

  // Fetch settings for the active tab (for tab-specific operations)
  const { data: tabSettings } = useQuery({
    queryKey: ["/api/settings", activeTab],
    queryFn: () => fetchSettings(activeTab),
  });

  // Use all settings for getting values, but prefer tab-specific settings
  const settings = tabSettings || allSettings;

  // Debug logging (remove in production)
  // console.log("Settings Debug:", { activeTab, allSettings, tabSettings, settings });

  // Mutation for saving settings
  const saveSettingMutation = useMutation({
    mutationFn: ({ category, key, value, description }: any) =>
      saveSetting(category, key, value, description),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    },
  });

  const settingsTabs = [
    { id: "general", label: "General", icon: SettingsIcon },
    { id: "company", label: "Company", icon: Building },
    { id: "appearance", label: "Appearance", icon: Palette },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "security", label: "Security", icon: Shield },
    { id: "database", label: "Database", icon: Database },
    { id: "localization", label: "Localization", icon: Globe },
  ];

  const handleSave = (section: string, formData?: FormData) => {
    if (!formData) {
      // If no form data provided, just show a success message (for backward compatibility)
      toast({
        title: "Settings Saved",
        description: `${section} settings have been updated successfully.`,
      });
      return;
    }

    const settingsToSave: any[] = [];

    // Extract form data and prepare settings
    for (const [key, value] of formData.entries()) {
      if (key && value) {
        settingsToSave.push({
          category: section,
          key: key,
          value: value,
          description: `${section} setting: ${key}`
        });
      }
    }

    if (settingsToSave.length === 0) {
      toast({
        title: "No Changes",
        description: "No settings to save.",
      });
      return;
    }

    // Save all settings
    Promise.all(
      settingsToSave.map(setting =>
        saveSettingMutation.mutateAsync(setting)
      )
    ).then(() => {
      toast({
        title: "Settings Saved",
        description: `${section} settings have been updated successfully.`,
      });
    }).catch(() => {
      toast({
        title: "Error",
        description: "Failed to save some settings. Please try again.",
        variant: "destructive",
      });
    });
  };

  // Helper function to get setting value
  const getSettingValue = (key: string, defaultValue: any = "", category?: string) => {
    // First try to find in current tab settings
    let setting = settings?.find((s: any) => s.key === key && (!category || s.category === category));

    // If not found and we have all settings, search there
    if (!setting && allSettings) {
      setting = allSettings.find((s: any) => s.key === key && (!category || s.category === category));
    }

    // If still not found and no category specified, search without category restriction
    if (!setting && !category && allSettings) {
      setting = allSettings.find((s: any) => s.key === key);
    }

    return setting ? setting.value : defaultValue;
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Application Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            handleSave("general", formData);
          }}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="app_name">Application Name</Label>
                <Input
                  id="app_name"
                  name="app_name"
                  defaultValue={getSettingValue("app_name", "JewelPro Billing")}
                />
              </div>
              <div>
                <Label htmlFor="version">Version</Label>
                <Input id="version" defaultValue="1.0.0" disabled />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                defaultValue={getSettingValue("description", "Comprehensive jewelry wholesale billing and inventory management system")}
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="auto_save" name="auto_save" defaultChecked />
              <Label htmlFor="auto_save">Enable auto-save</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="analytics" name="analytics" defaultChecked />
              <Label htmlFor="analytics">Enable usage analytics</Label>
            </div>

            <Button type="submit" disabled={saveSettingMutation.isPending}>
              {saveSettingMutation.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );

  const renderCompanySettings = () => (
    <CompanySettings 
      onSave={(data) => {
        // Save to database as well if needed
        const settingsToSave = [
          { category: "company", key: "name", value: data.name },
          { category: "company", key: "address", value: data.address },
          { category: "company", key: "phone", value: data.phone },
          { category: "company", key: "email", value: data.email },
          { category: "company", key: "gstin", value: data.gstin },
          { category: "company", key: "pan", value: data.pan },
          { category: "company", key: "stateCode", value: data.stateCode },
          { category: "company", key: "website", value: data.website },
          { category: "company", key: "bankName", value: data.bankName },
          { category: "company", key: "accountNumber", value: data.accountNumber },
          { category: "company", key: "ifscCode", value: data.ifscCode },
          { category: "company", key: "branch", value: data.branch },
        ];

        // Save all settings to database
        Promise.all(
          settingsToSave.map(setting =>
            saveSettingMutation.mutateAsync(setting)
          )
        ).catch((error) => {
          console.error("Error saving to database:", error);
          // Settings are still saved to localStorage by CompanySettings component
        });
      }}
    />
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Theme & Display</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="theme">Theme</Label>
            <Select defaultValue="light">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="primaryColor">Primary Color</Label>
            <Select defaultValue="emerald">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="emerald">Emerald Green</SelectItem>
                <SelectItem value="blue">Blue</SelectItem>
                <SelectItem value="purple">Purple</SelectItem>
                <SelectItem value="orange">Orange</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="fontSize">Font Size</Label>
            <Select defaultValue="medium">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="small">Small</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="large">Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch id="compactMode" />
            <Label htmlFor="compactMode">Compact Mode</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch id="animations" defaultChecked />
            <Label htmlFor="animations">Enable Animations</Label>
          </div>

          <Button onClick={() => handleSave("Appearance")}>Save Appearance</Button>
        </CardContent>
      </Card>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Notification Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>New Orders</Label>
                <p className="text-sm text-neutral-600">Notify when new orders are placed</p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>Low Stock Alerts</Label>
                <p className="text-sm text-neutral-600">Alert when inventory is running low</p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>Payment Received</Label>
                <p className="text-sm text-neutral-600">Notify when payments are received</p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>Rate Changes</Label>
                <p className="text-sm text-neutral-600">Alert on significant metal rate changes</p>
              </div>
              <Switch />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>System Updates</Label>
                <p className="text-sm text-neutral-600">Notify about system updates and maintenance</p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mt-6">
            <div>
              <Label htmlFor="emailNotifications">Email Notifications</Label>
              <Input id="emailNotifications" type="email" placeholder="Enter email for notifications" />
            </div>
            <div>
              <Label htmlFor="smsNotifications">SMS Notifications</Label>
              <Input id="smsNotifications" type="tel" placeholder="Enter phone for SMS alerts" />
            </div>
          </div>

          <Button onClick={() => handleSave("Notifications")}>Save Notification Settings</Button>
        </CardContent>
      </Card>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Security & Access</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="currentPassword">Current Password</Label>
            <Input id="currentPassword" type="password" placeholder="Enter current password" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="newPassword">New Password</Label>
              <Input id="newPassword" type="password" placeholder="Enter new password" />
            </div>
            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input id="confirmPassword" type="password" placeholder="Confirm new password" />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch id="twoFactor" />
            <Label htmlFor="twoFactor">Enable Two-Factor Authentication</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch id="sessionTimeout" defaultChecked />
            <Label htmlFor="sessionTimeout">Auto-logout after inactivity</Label>
          </div>

          <div>
            <Label htmlFor="sessionDuration">Session Duration (minutes)</Label>
            <Select defaultValue="60">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="60">1 hour</SelectItem>
                <SelectItem value="120">2 hours</SelectItem>
                <SelectItem value="480">8 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={() => handleSave("Security")}>Update Security Settings</Button>
        </CardContent>
      </Card>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Database & Backup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch id="autoBackup" defaultChecked />
            <Label htmlFor="autoBackup">Enable automatic backups</Label>
          </div>

          <div>
            <Label htmlFor="backupFrequency">Backup Frequency</Label>
            <Select defaultValue="daily">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Hourly</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="retentionPeriod">Backup Retention (days)</Label>
            <Select defaultValue="30">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 days</SelectItem>
                <SelectItem value="30">30 days</SelectItem>
                <SelectItem value="90">90 days</SelectItem>
                <SelectItem value="365">1 year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex space-x-3">
            <Button variant="outline">Create Backup Now</Button>
            <Button variant="outline">Restore from Backup</Button>
          </div>

          <Separator />

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-semibold text-red-800 mb-2">Danger Zone</h4>
            <p className="text-sm text-red-600 mb-3">
              The following actions are irreversible. Please proceed with caution.
            </p>
            <div className="flex space-x-3">
              <Button variant="destructive" size="sm">Clear All Data</Button>
              <Button variant="destructive" size="sm">Reset Application</Button>
            </div>
          </div>

          <Button onClick={() => handleSave("Database")}>Save Database Settings</Button>
        </CardContent>
      </Card>
    </div>
  );

  const renderLocalizationSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Language & Region</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="language">Language</Label>
            <Select defaultValue="en">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="hi">Hindi</SelectItem>
                <SelectItem value="ta">Tamil</SelectItem>
                <SelectItem value="te">Telugu</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="currency">Currency</Label>
            <Select defaultValue="inr">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="inr">Indian Rupee (₹)</SelectItem>
                <SelectItem value="usd">US Dollar ($)</SelectItem>
                <SelectItem value="eur">Euro (€)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="timezone">Timezone</Label>
            <Select defaultValue="asia_kolkata">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asia_kolkata">Asia/Kolkata (IST)</SelectItem>
                <SelectItem value="utc">UTC</SelectItem>
                <SelectItem value="america_new_york">America/New_York (EST)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="dateFormat">Date Format</Label>
            <Select defaultValue="dd_mm_yyyy">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dd_mm_yyyy">DD/MM/YYYY</SelectItem>
                <SelectItem value="mm_dd_yyyy">MM/DD/YYYY</SelectItem>
                <SelectItem value="yyyy_mm_dd">YYYY-MM-DD</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="numberFormat">Number Format</Label>
            <Select defaultValue="indian">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="indian">Indian (1,00,000)</SelectItem>
                <SelectItem value="international">International (100,000)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={() => handleSave("Localization")}>Save Language Settings</Button>
        </CardContent>
      </Card>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return renderGeneralSettings();
      case "company":
        return renderCompanySettings();
      case "appearance":
        return renderAppearanceSettings();
      case "notifications":
        return renderNotificationSettings();
      case "security":
        return renderSecuritySettings();
      case "database":
        return renderDatabaseSettings();
      case "localization":
        return renderLocalizationSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Settings" />
        
        <main className="flex-1 overflow-auto p-6">
          <div className="flex gap-8">
            {/* Settings Navigation */}
            <div className="w-64 shrink-0">
              <div className="bg-white rounded-lg border p-2">
                <nav className="space-y-1">
                  {settingsTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? "bg-[hsl(154,50%,20%)] text-white"
                          : "text-neutral-700 hover:bg-neutral-100"
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              {renderTabContent()}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}